/**
 * 财务管理模块 - V2版本
 * 按照API封装规范文档实现财务管理相关接口
 */

import { get, post } from '../index'

export default {
  /**
   * 获取财务列表
   * @param {Object} querys 查询参数
   * @param {number} querys.type 类型，1收入，2支出
   * @param {number} querys.category 分类，1订单收入，2佣金支出，3提现支出，4退款支出
   * @param {string} querys.startTime 开始时间，非必填
   * @param {string} querys.endTime 结束时间，非必填
   * @param {number} querys.pageNum 当前页数，可选，默认1
   * @param {number} querys.pageSize 每页数量，可选，默认10
   * @returns {Promise} 返回财务列表数据
   */
  financeList(querys) {
    console.log('💰 财务列表API-V2请求参数:', querys)
    return get('/api/admin/finance/list', querys)
  },

  /**
   * 获取财务详情
   * @param {Object} querys 查询参数
   * @param {number} querys.id 财务记录ID
   * @returns {Promise} 返回财务详情数据
   */
  financeInfo(querys) {
    if (!querys || !querys.id) {
      return Promise.reject(new Error('财务记录ID不能为空'))
    }
    console.log('🔍 获取财务详情API-V2请求:', querys)
    return get(`/api/admin/finance/info/${querys.id}`)
  },

  /**
   * 获取财务统计
   * @param {Object} querys 查询参数
   * @param {string} querys.startTime 开始时间
   * @param {string} querys.endTime 结束时间
   * @param {number} querys.type 统计类型，1日统计，2月统计，3年统计
   * @returns {Promise} 返回财务统计数据
   */
  financeStatistics(querys) {
    console.log('📊 财务统计API-V2请求参数:', querys)
    return get('/api/admin/finance/statistics', querys)
  },

  /**
   * 导出财务报表
   * @param {Object} querys 导出参数
   * @param {string} querys.startTime 开始时间
   * @param {string} querys.endTime 结束时间
   * @param {number} querys.type 报表类型，1收支明细，2统计报表
   * @returns {Promise} 返回导出结果
   */
  financeExport(querys) {
    console.log('📤 导出财务报表API-V2请求参数:', querys)
    return post('/api/admin/finance/export', querys)
  },

  /**
   * 获取提现列表
   * @param {Object} querys 查询参数
   * @param {number} querys.status 提现状态，1申请中，2已通过，3已拒绝，4已到账
   * @param {number} querys.userType 用户类型，1普通用户，2分销商，3师傅
   * @param {string} querys.userPhone 用户手机号，非必填
   * @param {string} querys.startTime 开始时间，非必填
   * @param {string} querys.endTime 结束时间，非必填
   * @param {number} querys.pageNum 当前页数，可选，默认1
   * @param {number} querys.pageSize 每页数量，可选，默认10
   * @returns {Promise} 返回提现列表数据
   */
  withdrawList(querys) {
    console.log('💸 提现列表API-V2请求参数:', querys)
    return get('/api/admin/finance/withdraw/list', querys)
  },

  /**
   * 获取提现详情
   * @param {Object} querys 查询参数
   * @param {number} querys.id 提现记录ID
   * @returns {Promise} 返回提现详情数据
   */
  withdrawInfo(querys) {
    if (!querys || !querys.id) {
      return Promise.reject(new Error('提现记录ID不能为空'))
    }
    console.log('🔍 获取提现详情API-V2请求:', querys)
    return get(`/api/admin/finance/withdraw/info/${querys.id}`)
  },

  /**
   * 审核提现申请
   * @param {Object} querys 审核参数
   * @param {number} querys.id 提现记录ID
   * @param {number} querys.status 审核状态，2通过，3拒绝
   * @param {string} querys.remark 审核备注
   * @returns {Promise} 返回审核结果
   */
  withdrawHandle(querys) {
    if (!querys || !querys.id) {
      return Promise.reject(new Error('提现记录ID不能为空'))
    }

    if (![2, 3].includes(querys.status)) {
      return Promise.reject(new Error('审核状态无效'))
    }

    console.log('✅ 审核提现申请API-V2请求:', querys)
    return post(`/api/admin/finance/withdraw/handle/${querys.id}`, querys)
  },

  /**
   * 确认提现到账
   * @param {Object} querys 确认参数
   * @param {number} querys.id 提现记录ID
   * @param {string} querys.transactionNo 交易流水号
   * @returns {Promise} 返回确认结果
   */
  withdrawConfirm(querys) {
    if (!querys || !querys.id) {
      return Promise.reject(new Error('提现记录ID不能为空'))
    }

    console.log('✅ 确认提现到账API-V2请求:', querys)
    return post(`/api/admin/finance/withdraw/confirm/${querys.id}`, querys)
  },

  /**
   * 获取储值列表
   * @param {Object} querys 查询参数
   * @param {number} querys.type 储值类型，1充值，2消费，3退款
   * @param {string} querys.userPhone 用户手机号，非必填
   * @param {string} querys.startTime 开始时间，非必填
   * @param {string} querys.endTime 结束时间，非必填
   * @param {number} querys.pageNum 当前页数，可选，默认1
   * @param {number} querys.pageSize 每页数量，可选，默认10
   * @returns {Promise} 返回储值列表数据
   */
  storedList(querys) {
    console.log('💳 储值列表API-V2请求参数:', querys)
    return get('/api/admin/finance/stored/list', querys)
  },

  /**
   * 获取储值详情
   * @param {Object} querys 查询参数
   * @param {number} querys.id 储值记录ID
   * @returns {Promise} 返回储值详情数据
   */
  storedInfo(querys) {
    if (!querys || !querys.id) {
      return Promise.reject(new Error('储值记录ID不能为空'))
    }
    console.log('🔍 获取储值详情API-V2请求:', querys)
    return get(`/api/admin/finance/stored/info/${querys.id}`)
  },

  /**
   * 手动充值
   * @param {Object} querys 充值参数
   * @param {number} querys.userId 用户ID
   * @param {number} querys.amount 充值金额
   * @param {string} querys.remark 充值备注
   * @returns {Promise} 返回充值结果
   */
  storedRecharge(querys) {
    if (!querys || !querys.userId || !querys.amount) {
      return Promise.reject(new Error('用户ID和充值金额不能为空'))
    }

    if (querys.amount <= 0) {
      return Promise.reject(new Error('充值金额必须大于0'))
    }

    console.log('💰 手动充值API-V2请求:', querys)
    return post('/api/admin/finance/stored/recharge', querys)
  },

  /**
   * 获取用户余额
   * @param {Object} querys 查询参数
   * @param {number} querys.userId 用户ID
   * @returns {Promise} 返回用户余额数据
   */
  userBalance(querys) {
    if (!querys || !querys.userId) {
      return Promise.reject(new Error('用户ID不能为空'))
    }

    console.log('💰 获取用户余额API-V2请求:', querys)
    return get(`/api/admin/finance/balance/${querys.userId}`)
  },

  /**
   * 获取财务概览
   * @param {Object} querys 查询参数
   * @param {string} querys.date 查询日期，格式：YYYY-MM-DD
   * @returns {Promise} 返回财务概览数据
   */
  financeOverview(querys) {
    console.log('📊 财务概览API-V2请求参数:', querys)
    return get('/api/admin/finance/overview', querys)
  },

  // ==================== 提现管理相关接口 ====================

  /**
   * 提现管理列表
   * @param {Object} querys 查询参数
   * @param {string} querys.code 提现单号，非必填
   * @param {number} querys.userId 用户id，非必填
   * @param {number} querys.coachId 师傅id，非必填
   * @param {number} querys.status 状态 -1内部错误，1已提现，未领取 2到账，3失败，4关闭，非必填
   * @param {number} querys.type 提现类型：1是车费 2服务费 3加盟 4用户分销，非必填
   * @param {number} querys.online 付款方式：1线上 0线下，非必填
   * @param {number} querys.sourceType 来源：1=APP，2=小程序，非必填
   * @param {number} querys.cashToType 提现到：1=微信，2=支付宝，3=银行卡，非必填
   * @param {string} querys.startTime 提现申请开始时间，非必填
   * @param {string} querys.endTime 提现申请结束时间，非必填
   * @param {number} querys.pageNum 当前页数，非必填
   * @param {number} querys.pageSize 每页数量，非必填
   * @returns {Promise} 返回提现管理列表数据
   */
  walletList(querys) {
    console.log('💸 提现管理列表API-V2请求参数:', querys)
    return get('/api/admin/wallet/list', querys)
  },

  /**
   * 提现汇总统计
   * @param {Object} querys 查询参数（与列表接口相同的筛选条件）
   * @returns {Promise} 返回提现汇总统计数据
   */
  walletStats(querys) {
    console.log('📊 提现汇总统计API-V2请求参数:', querys)
    return get('/api/admin/wallet/stats', querys)
  },



  /**
   * 审核提现记录
   * @param {Object} querys 审核参数
   * @param {number} querys.id 提现记录ID
   * @param {number} querys.lock 审核状态（0未审核，1审核通过，2审核拒绝）
   * @param {number} querys.adminId 管理员ID
   * @returns {Promise} 返回审核结果
   */
  walletAudit(querys) {
    if (!querys || !querys.id) {
      return Promise.reject(new Error('提现记录ID不能为空'))
    }

    if (![0, 1, 2].includes(querys.lock)) {
      return Promise.reject(new Error('审核状态无效'))
    }

    console.log('✅ 审核提现记录API-V2请求:', querys)

    // 创建FormData对象，使用form-data格式
    const formData = new FormData()
    formData.append('id', querys.id)
    formData.append('lock', querys.lock)
    formData.append('adminId', querys.adminId)
    formData.append('text', querys.text)

    return post('/api/admin/wallet/audit', formData, 'multipart/form-data')
  },

  /**
   * 查询运营账户余额
   * @returns {Promise} 返回运营账户余额
   */
  operatorBalance() {
    console.log('💰 查询运营账户余额API-V2请求')
    return get('/api/admin/wallet/operatorBalance')
  },

  /**
   * 设置/调整运营账户余额
   * @param {Object} querys 设置参数
   * @param {string} querys.amount 金额
   * @returns {Promise} 返回设置结果
   */
  operatorBalanceSet(querys) {
    if (!querys || !querys.amount) {
      return Promise.reject(new Error('金额不能为空'))
    }

    console.log('💰 设置运营账户余额API-V2请求:', querys)

    // 创建FormData对象，使用form-data格式
    const formData = new FormData()
    formData.append('amount', querys.amount)

    return post('/api/admin/wallet/operatorBalance/set', formData, 'multipart/form-data')
  },

  /**
   * 增加运营账户余额
   * @param {Object} querys 增加参数
   * @param {string} querys.amount 金额
   * @returns {Promise} 返回增加结果
   */
  operatorBalanceIncrease(querys) {
    if (!querys || !querys.amount) {
      return Promise.reject(new Error('金额不能为空'))
    }

    console.log('💰 增加运营账户余额API-V2请求:', querys)

    // 创建FormData对象，使用form-data格式
    const formData = new FormData()
    formData.append('amount', querys.amount)

    return post('/api/admin/wallet/operatorBalance/increase', formData, 'multipart/form-data')
  },

  /**
   * 扣减运营账户余额
   * @param {Object} querys 扣减参数
   * @param {string} querys.amount 金额
   * @returns {Promise} 返回扣减结果
   */
  operatorBalanceDecrease(querys) {
    if (!querys || !querys.amount) {
      return Promise.reject(new Error('金额不能为空'))
    }

    console.log('💰 扣减运营账户余额API-V2请求:', querys)

    // 创建FormData对象，使用form-data格式
    const formData = new FormData()
    formData.append('amount', querys.amount)

    return post('/api/admin/wallet/operatorBalance/decrease', formData, 'multipart/form-data')
  },

  // ==================== 财务流水管理相关接口 ====================

  /**
   * 财务流水明细查询
   * @param {Object} querys 查询参数
   * @param {number} querys.type 流水类型，可选，0服务收入 1分销 2服务提现 3佣金提现
   * @param {number} querys.userId 用户ID，可选
   * @param {number} querys.coachId 师傅ID，可选
   * @param {string} querys.startTime 开始时间，可选
   * @param {string} querys.endTime 结束时间，可选
   * @param {number} querys.pageNum 页数，可选，默认1
   * @param {number} querys.pageSize 数量，可选，默认10
   * @returns {Promise} 返回财务流水列表数据
   */
  list(querys) {
    console.log('💰 财务流水明细查询API-V2请求参数:', querys)
    return post('/api/admin/finance/list', querys)
  },

  /**
   * 财务流水明细导出Excel
   * @param {Object} querys 查询参数
   * @param {number} querys.type 流水类型，可选，0服务收入 1分销 2服务提现 3佣金提现
   * @param {number} querys.userId 用户ID，可选
   * @param {number} querys.coachId 师傅ID，可选
   * @param {string} querys.startTime 开始时间，可选
   * @param {string} querys.endTime 结束时间，可选
   * @returns {Promise} 返回导出结果
   */
  export(querys) {
    console.log('📤 财务流水明细导出API-V2请求参数:', querys)
    return post('/api/admin/finance/export', querys)
  },

  // ==================== 历史提现管理相关接口 ====================

  /**
   * 历史提现列表查询
   * @param {Object} querys 查询参数
   * @param {number} querys.userId 用户ID，非必填
   * @param {number} querys.status 状态 -1内部错误，1已提现未领取，2到账，3失败，4关闭，非必填
   * @param {number} querys.type 提现类型：1车费，2服务费，3加盟，4用户分销，非必填
   * @param {string} querys.beginTime 提现申请开始时间，非必填
   * @param {string} querys.endTime 提现申请结束时间，非必填
   * @param {number} querys.pageNum 当前页数，非必填，默认1
   * @param {number} querys.pageSize 每页数量，非必填，默认10
   * @returns {Promise} 返回历史提现列表数据
   */
  withdrawHistoryList(querys) {
    console.log('📋 历史提现列表API-V2请求参数:', querys)
    return get('/api/admin/wallet/history/page', querys)
  },

  /**
   * 历史提现列表导出
   * @param {Object} querys 查询参数
   * @param {number} querys.userId 用户ID，非必填
   * @param {number} querys.status 状态 -1内部错误，1已提现未领取，2到账，3失败，4关闭，非必填
   * @param {number} querys.type 提现类型：1车费，2服务费，3加盟，4用户分销，非必填
   * @param {string} querys.beginTime 提现申请开始时间，非必填
   * @param {string} querys.endTime 提现申请结束时间，非必填
   * @returns {Promise} 返回导出结果
   */
  withdrawHistoryExport(querys) {
    console.log('📤 历史提现列表导出API-V2请求参数:', querys)
    return get('/api/admin/wallet/history/export', querys)
  }
}
