<!--
  评价管理页面
  根据开发文档重新开发，实现评价列表展示、详情查看、删除评价和修改评价状态功能
  API: GET /api/admin/comment/list
-->
<template>
  <div class="shop-evaluate">
    <!-- 顶部导航 -->
    <TopNav title="评价管理" />

    <div class="content-container">
      <!-- 搜索表单 -->
      <div class="search-form-container">
        <el-form
          ref="searchFormRef"
          :model="searchForm"
          :inline="true"
          class="search-form"
        >
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="用户ID" prop="userId">
                <el-input
                  size="default"
                  v-model="searchForm.userId"
                  placeholder="请输入用户ID"
                  clearable
                  style="width: 150px"
                />
              </el-form-item>

              <el-form-item label="师傅ID" prop="coachId">
                <el-input
                  size="default"
                  v-model="searchForm.coachId"
                  placeholder="请输入师傅ID"
                  clearable
                  style="width: 150px"
                />
              </el-form-item>

              <el-form-item label="订单ID" prop="orderId">
                <el-input
                  size="default"
                  v-model="searchForm.orderId"
                  placeholder="请输入订单ID"
                  clearable
                  style="width: 150px"
                />
              </el-form-item>

              <el-form-item label="星级" prop="star">
                <el-select
                  v-model="searchForm.star"
                  placeholder="请选择星级"
                  clearable
                  style="width: 120px"
                >
                  <el-option label="1星" :value="1" />
                  <el-option label="2星" :value="2" />
                  <el-option label="3星" :value="3" />
                  <el-option label="4星" :value="4" />
                  <el-option label="5星" :value="5" />
                </el-select>
              </el-form-item>

              <el-form-item label="评价内容" prop="text">
                <el-input
                  size="default"
                  v-model="searchForm.text"
                  placeholder="请输入评价内容"
                  clearable
                  style="width: 200px"
                />
              </el-form-item>

              <el-form-item>
                <LbButton type="primary" @click="handleSearch" :loading="loading">
                  <el-icon><Search /></el-icon>
                  搜索
                </LbButton>
                <LbButton @click="handleReset">
                  <el-icon><RefreshLeft /></el-icon>
                  重置
                </LbButton>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>

      <!-- 数据表格 -->
      <div class="table-container">
        <el-table
          :data="tableData"
          v-loading="loading"
          stripe
          border
          style="width: 100%"
          :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
        >
          <el-table-column prop="id" label="ID" width="80" align="center" />
          <el-table-column prop="userId" label="用户ID" width="100" align="center" />
          <el-table-column prop="coachId" label="师傅ID" width="100" align="center" />
          <el-table-column prop="orderId" label="订单ID" width="100" align="center" />
          
          <el-table-column label="星级" width="120" align="center">
            <template #default="{ row }">
              <el-rate
                v-model="row.star"
                disabled
                show-score
                text-color="#ff9900"
                score-template="{value}星"
              />
            </template>
          </el-table-column>

          <el-table-column prop="text" label="评价内容" min-width="200">
            <template #default="{ row }">
              <span v-if="row.text">{{ row.text }}</span>
              <span v-else style="color: #999">暂无评价内容</span>
            </template>
          </el-table-column>

          <el-table-column label="评价图片" width="120" align="center">
            <template #default="{ row }">
              <LbImage
                v-if="row.imgs"
                :src="row.imgs"
                :width="60"
                :height="60"
                fit="cover"
                preview-disabled
                style="border-radius: 4px"
              />
              <span v-else style="color: #999">无图片</span>
            </template>
          </el-table-column>

          <el-table-column label="状态" width="100" align="center">
            <template #default="{ row }">
              <el-tag :type="row.status === 1 ? 'success' : 'danger'">
                {{ row.status === 1 ? '正常' : '隐藏' }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column prop="createTime" label="创建时间" width="180" align="center" />

          <el-table-column label="操作" width="200" align="center" fixed="right">
            <template #default="{ row }">
              <LbButton type="primary" size="small" @click="handleDetail(row)">
                详情
              </LbButton>
              <LbButton 
                :type="row.status === 1 ? 'warning' : 'success'" 
                size="small" 
                @click="handleStatusChange(row)"
              >
                {{ row.status === 1 ? '隐藏' : '显示' }}
              </LbButton>
              <LbButton type="danger" size="small" @click="handleDelete(row)">
                删除
              </LbButton>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-container">
          <LbPage
            :page="searchForm.pageNum"
            :page-size="searchForm.pageSize"
            :total="total"
            @handleSizeChange="handleSizeChange"
            @handleCurrentChange="handleCurrentChange"
          />
        </div>
      </div>
    </div>

    <!-- 详情弹窗 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="评价详情"
      width="600px"
      :before-close="handleDetailClose"
    >
      <div v-if="detailData" class="detail-content">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="评价ID">{{ detailData.id }}</el-descriptions-item>
          <el-descriptions-item label="用户ID">{{ detailData.userId }}</el-descriptions-item>
          <el-descriptions-item label="师傅ID">{{ detailData.coachId || '无' }}</el-descriptions-item>
          <el-descriptions-item label="订单ID">{{ detailData.orderId || '无' }}</el-descriptions-item>
          <el-descriptions-item label="星级">
            <el-rate v-model="detailData.star" disabled show-score />
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="detailData.status === 1 ? 'success' : 'danger'">
              {{ detailData.status === 1 ? '正常' : '隐藏' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="创建时间" :span="2">{{ detailData.createTime }}</el-descriptions-item>
          <el-descriptions-item label="评价内容" :span="2">
            {{ detailData.text || '暂无评价内容' }}
          </el-descriptions-item>
          <el-descriptions-item label="评价图片" :span="2" v-if="detailData.imgs">
            <LbImage
              :src="detailData.imgs"
              :width="200"
              :height="200"
              fit="cover"
              style="border-radius: 8px"
            />
          </el-descriptions-item>
        </el-descriptions>
      </div>
      
      <template #footer>
        <span class="dialog-footer">
          <LbButton @click="detailDialogVisible = false">关闭</LbButton>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, getCurrentInstance } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, RefreshLeft } from '@element-plus/icons-vue'
import TopNav from '@/components/common/TopNav.vue'
import LbButton from '@/components/common/LbButton.vue'
import LbImage from '@/components/common/LbImage.vue'
import LbPage from '@/components/common/LbPage.vue'

const { proxy } = getCurrentInstance()

// 响应式数据
const loading = ref(false)
const tableData = ref([])
const total = ref(0)
const detailDialogVisible = ref(false)
const detailData = ref(null)

// 搜索表单
const searchFormRef = ref()
const searchForm = reactive({
  userId: '',
  coachId: '',
  orderId: '',
  star: '',
  text: '',
  pageNum: 1,
  pageSize: 10
})

// 获取评价列表
const getTableDataList = async () => {
  try {
    loading.value = true
    console.log('🔍 获取评价列表，参数:', searchForm)

    // 构建请求参数，只传递有值的参数
    const params = {
      pageNum: searchForm.pageNum,
      pageSize: searchForm.pageSize
    }

    // 只有在搜索时才添加其他参数
    if (searchForm.userId) params.userId = searchForm.userId
    if (searchForm.coachId) params.coachId = searchForm.coachId
    if (searchForm.orderId) params.orderId = searchForm.orderId
    if (searchForm.star) params.star = searchForm.star
    if (searchForm.text) params.text = searchForm.text

    const response = await proxy.$api.shop.commentList(params)
    console.log('📋 评价列表响应:', response)

    if (response.code === '200') {
      tableData.value = response.data.list || []
      total.value = response.data.totalCount || 0
      console.log(`✅ 评价列表加载成功，共 ${total.value} 条数据`)
    } else {
      ElMessage.error(response.msg || '获取评价列表失败')
    }
  } catch (error) {
    console.error('❌ 获取评价列表失败:', error)
    ElMessage.error('获取评价列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索处理
const handleSearch = () => {
  searchForm.pageNum = 1
  getTableDataList()
}

// 重置搜索
const handleReset = () => {
  searchFormRef.value?.resetFields()
  Object.assign(searchForm, {
    userId: '',
    coachId: '',
    orderId: '',
    star: '',
    text: '',
    pageNum: 1,
    pageSize: 10
  })
  getTableDataList()
}

// 分页大小变化
const handleSizeChange = (size) => {
  searchForm.pageSize = size
  searchForm.pageNum = 1
  getTableDataList()
}

// 当前页变化
const handleCurrentChange = (page) => {
  searchForm.pageNum = page
  getTableDataList()
}

// 查看详情
const handleDetail = async (row) => {
  try {
    console.log('📄 查看评价详情:', row.id)

    const response = await proxy.$api.shop.commentDetail({ id: row.id })
    console.log('📄 评价详情响应:', response)

    if (response.code === '200') {
      detailData.value = response.data
      detailDialogVisible.value = true
    } else {
      ElMessage.error(response.msg || '获取评价详情失败')
    }
  } catch (error) {
    console.error('❌ 获取评价详情失败:', error)
    ElMessage.error('获取评价详情失败')
  }
}

// 关闭详情弹窗
const handleDetailClose = () => {
  detailDialogVisible.value = false
  detailData.value = null
}

// 删除评价
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除ID为 ${row.id} 的评价吗？`,
      '删除确认',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    console.log('🗑️ 删除评价:', row.id)
    const response = await proxy.$api.shop.commentDelete({ id: row.id })

    if (response.code === '200') {
      ElMessage.success('删除成功')
      getTableDataList()
    } else {
      ElMessage.error(response.msg || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('❌ 删除评价失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 修改评价状态
const handleStatusChange = async (row) => {
  const newStatus = row.status === 1 ? 0 : 1
  const statusText = newStatus === 1 ? '显示' : '隐藏'

  try {
    await ElMessageBox.confirm(
      `确定要${statusText}这条评价吗？`,
      '状态修改确认',
      {
        confirmButtonText: `确定${statusText}`,
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    console.log('🔄 修改评价状态:', row.id, '新状态:', newStatus)
    const response = await proxy.$api.shop.commentStatus({
      id: row.id,
      status: newStatus
    })

    if (response.code === '200') {
      ElMessage.success(`${statusText}成功`)
      getTableDataList()
    } else {
      ElMessage.error(response.msg || `${statusText}失败`)
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('❌ 修改评价状态失败:', error)
      ElMessage.error(`${statusText}失败`)
    }
  }
}

// 组件挂载时加载数据
onMounted(() => {
  console.log('🚀 评价管理页面初始化')
  getTableDataList()
})
</script>

<style scoped>
.shop-evaluate {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.content-container {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.search-form-container {
  margin-bottom: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 6px;
}

.search-form .el-form-item {
  margin-bottom: 10px;
}

.table-container {
  margin-top: 20px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.detail-content {
  padding: 10px 0;
}

.detail-content .el-descriptions {
  margin-top: 10px;
}

.dialog-footer {
  display: flex;
  justify-content: center;
  gap: 10px;
}
</style>
