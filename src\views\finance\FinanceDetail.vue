<!--
  财务详情页面
 /src/view/finance/finance/detail.vue重构
-->

<template>
  <div class="lb-finance-detail">
    <TopNav title="财务详情" :isBack="true" />
    <div class="page-main">
      <el-row class="mb-lg">
        <LbButton
          size="default"
          type="primary"
          :loading="downloadLoading"
          @click="exportOrder"
        >
          导出
        </LbButton>
      </el-row>
      
      <el-row>
        <div class="flex-wrap mb-lg">
          <div class="flex-1 mr-lg pr-lg">师傅名称：{{ financeInfo.coach_name }}</div>
          <div>账单周期：{{ formatDate(searchForm.start_time) }} 至 {{ formatDate(searchForm.end_time) }}</div>
        </div>
      </el-row>
      
      <!-- 财务统计卡片 -->
      <el-row :gutter="20" class="stats-cards">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-value">¥{{ financeInfo.total_income || 0 }}</div>
              <div class="stat-label">总收入</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-value">¥{{ financeInfo.total_refund || 0 }}</div>
              <div class="stat-label">总退款</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-value">¥{{ financeInfo.total_withdraw || 0 }}</div>
              <div class="stat-label">总提现</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-value">¥{{ financeInfo.current_balance || 0 }}</div>
              <div class="stat-label">当前余额</div>
            </div>
          </el-card>
        </el-col>
      </el-row>
      
      <!-- 收支明细表格 -->
      <el-table
        v-loading="loading"
        :data="tableData"
        :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
        style="width: 100%; margin-top: 20px;"
      >
        <el-table-column prop="date" label="收支时间" width="170">
          <template #default="scope">
            <div>{{ formatDate(scope.row.date, 1) }}</div>
            <div>{{ formatDate(scope.row.date, 2) }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="type" label="类型" width="120">
          <template #default="scope">
            <el-tag :type="getTypeColor(scope.row.type)" size="default">
              {{ getTypeText(scope.row.type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="order_price" label="订单收入（元）" width="150">
          <template #default="scope">
            <span v-if="scope.row.order_price > 0" style="color: #67c23a; font-weight: 600;">
              +¥{{ scope.row.order_price }}
            </span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column prop="refund_price" label="订单退款（元）" width="150">
          <template #default="scope">
            <span v-if="scope.row.refund_price > 0" style="color: #f56c6c; font-weight: 600;">
              -¥{{ scope.row.refund_price }}
            </span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column prop="wallet_price" label="提现（元）" width="150">
          <template #default="scope">
            <span v-if="scope.row.wallet_price > 0" style="color: #e6a23c; font-weight: 600;">
              -¥{{ scope.row.wallet_price }}
            </span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column prop="balance" label="余额（元）" width="150">
          <template #default="scope">
            <span style="color: #409eff; font-weight: 600;">¥{{ scope.row.balance }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="remark" label="备注" min-width="200" />
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination-section">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
      
      <div class="space-lg mt-lg mb-lg"></div>
      <LbButton type="primary" @click="$router.go(-1)">
        返回
      </LbButton>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import TopNav from '@/components/common/TopNav.vue'
import LbButton from '@/components/common/LbButton.vue'

const route = useRoute()

// 响应式数据
const loading = ref(false)
const downloadLoading = ref(false)
const tableData = ref([])

// 搜索表单
const searchForm = reactive({
  coach_id: 0,
  start_time: '',
  end_time: ''
})

// 分页数据
const pagination = reactive({
  page: 1,
  pageSize: 10,
  total: 0
})

// 财务信息
const financeInfo = reactive({
  coach_name: '',
  total_income: 0,
  total_refund: 0,
  total_withdraw: 0,
  current_balance: 0
})

// 方法
const getFinanceDetail = async () => {
  loading.value = true
  
  try {
    const params = new URLSearchParams({
      coach_id: searchForm.coach_id,
      start_time: searchForm.start_time,
      end_time: searchForm.end_time,
      page: pagination.page,
      pageSize: pagination.pageSize
    })
    
    const response = await fetch(`/api/finance/detail?${params}`)
    const result = await response.json()

    if (result.code === 200) {
      tableData.value = result.data.list || []
      pagination.total = result.data.total || 0
      Object.assign(financeInfo, result.data.info || {})
    } else {
      ElMessage.error(result.message || '获取数据失败')
    }
  } catch (error) {
    console.error('获取财务详情失败:', error)
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

const exportOrder = async () => {
  downloadLoading.value = true
  
  try {
    const params = new URLSearchParams({
      coach_id: searchForm.coach_id,
      start_time: searchForm.start_time,
      end_time: searchForm.end_time
    })
    
    const response = await fetch(`/api/finance/export-detail?${params}`)
    const result = await response.json()
    
    if (result.code === 200) {
      ElMessage.success('导出成功')
      // 这里可以添加下载文件的逻辑
    } else {
      ElMessage.error(result.message || '导出失败')
    }
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败')
  } finally {
    downloadLoading.value = false
  }
}

const getTypeColor = (type) => {
  const typeMap = {
    1: 'success',  // 收入
    2: 'danger',   // 退款
    3: 'warning'   // 提现
  }
  return typeMap[type] || 'info'
}

const getTypeText = (type) => {
  const typeMap = {
    1: '订单收入',
    2: '订单退款',
    3: '提现'
  }
  return typeMap[type] || '其他'
}

const formatDate = (timestamp, type) => {
  if (!timestamp) return ''
  const date = new Date(timestamp)
  if (type === 1) {
    return date.toLocaleDateString()
  } else if (type === 2) {
    return date.toLocaleTimeString()
  } else {
    return date.toLocaleDateString()
  }
}

const handleSizeChange = (size) => {
  pagination.pageSize = size
  getFinanceDetail()
}

const handleCurrentChange = (page) => {
  pagination.page = page
  getFinanceDetail()
}

// 生命周期
onMounted(() => {
  // 从路由参数获取数据
  const { coach_id, start_time, end_time } = route.query
  if (coach_id) {
    searchForm.coach_id = coach_id
    searchForm.start_time = start_time || ''
    searchForm.end_time = end_time || ''
    getFinanceDetail()
  }
})
</script>

<style scoped>
.lb-finance-detail {
  padding: 20px;
  background-color: #fff;
  min-height: calc(100vh - 60px);
}

.mb-lg {
  margin-bottom: 20px;
}

.mr-lg {
  margin-right: 20px;
}

.pr-lg {
  padding-right: 20px;
}

.mt-lg {
  margin-top: 20px;
}

.space-lg {
  height: 20px;
}

.flex-wrap {
  display: flex;
  align-items: center;
}

.flex-1 {
  flex: 1;
}

.stats-cards {
  margin-bottom: 20px;
}

/* 统计内容布局 */
.stat-content {
  text-align: center;
  padding: 10px 0;
}

.stats-value {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 8px;
}

.stats-label {
  font-size: 14px;
  color: #909399;
}

.pagination-section {
  margin-top: 20px;
  text-align: right;
}

@media (max-width: 768px) {
  .lb-finance-detail {
    padding: 10px;
  }
  
  .flex-wrap {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
  
  .stats-cards .el-col {
    margin-bottom: 10px;
  }
  
  .pagination-section {
    text-align: center;
  }
}
</style>
