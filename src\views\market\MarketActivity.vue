<!--
  公告设置页面
 /src/view/market/atv.vue重构
-->

<template>
  <div class="lb-edit-card">
    <TopNav />
    <div class="page-main">
      <LbTips>
        <div>推荐人：邀请一定数量的好友授权用户信息及手机号后才可获得相应奖励</div>
        <div style="margin-top: 8px;">被推荐人：被推荐人授权用户信息及手机号后即可获得相应奖励</div>
      </LbTips>
      
      <el-form
        @submit.prevent
        :model="subForm"
        :rules="subFormRules"
        ref="subFormRef"
        label-width="140px"
        class="basic-form"
      >
        <el-form-item label="开启活动" prop="status">
          <el-radio-group v-model="subForm.status">
            <el-radio :value="1">开启</el-radio>
            <el-radio :value="0">关闭</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item label="分享图" prop="share_img">
          <LbCover
            :fileList="subForm.share_img"
            @selectedFiles="getCover"
          />
          <LbToolTips>图片建议尺寸: 652 * 652</LbToolTips>
        </el-form-item>
        
        <el-form-item label="活动时间" prop="time">
          <el-date-picker
            v-model="subForm.time"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            value-format="YYYY-MM-DD HH:mm:ss"
            :disabled-date="disabledDate"
          />
        </el-form-item>
        
        <el-form-item label="邀请好友数" prop="inv_user_num">
          <el-input-number
            v-model="subForm.inv_user_num"
            :controls="false"
            :min="1"
            :precision="0"
            placeholder="请输入邀请好友数"
            style="width: 200px;"
          />
        </el-form-item>
        
        <el-form-item label="邀请有效期" prop="inv_time">
          <el-input-number
            v-model="subForm.inv_time"
            :controls="false"
            :min="1"
            :precision="0"
            placeholder="请输入邀请有效期"
            style="width: 200px;"
          />
          <span style="margin-left: 8px;">小时</span>
        </el-form-item>
        
        <el-form-item label="发起活动次数" prop="atv_num">
          <el-input-number
            v-model="subForm.atv_num"
            :controls="false"
            :min="1"
            :precision="0"
            placeholder="请输入发起活动次数"
            style="width: 200px;"
          />
          <span style="margin-left: 8px;">次/人</span>
        </el-form-item>
        
        <el-form-item label="获得奖励人" prop="inv_user">
          <el-checkbox-group @change="changeCheckBox" v-model="checkList">
            <div
              v-for="(item, index) in authList"
              :key="index"
              style="display: inline-block; margin-right: 15px;"
            >
              <el-checkbox
                :disabled="item.is_check ? true : false"
                :label="item.title"
              />
              <LbToolTips v-if="item.tips">{{ item.tips }}</LbToolTips>
            </div>
          </el-checkbox-group>
        </el-form-item>
        
        <!-- 推荐人奖励设置 -->
        <template v-if="checkList.includes('推荐人')">
          <el-form-item label="推荐人奖励类型" prop="inv_reward_type">
            <el-radio-group v-model="subForm.inv_reward_type">
              <el-radio :value="1">卡券</el-radio>
              <el-radio :value="2">积分</el-radio>
            </el-radio-group>
          </el-form-item>
          
          <el-form-item label="推荐人奖励" v-if="subForm.inv_reward_type === 1">
            <LbButton type="primary" @click="showCouponDialog = true">
              选择卡券
            </LbButton>
            <div v-if="selectedCoupons.length > 0" style="margin-top: 8px;">
              <el-tag 
                v-for="coupon in selectedCoupons" 
                :key="coupon.id"
                closable
                @close="removeCoupon(coupon.id)"
                style="margin-right: 8px; margin-bottom: 8px;"
              >
                {{ coupon.title }}
              </el-tag>
            </div>
          </el-form-item>
          
          <el-form-item label="推荐人积分" v-if="subForm.inv_reward_type === 2">
            <el-input-number
              v-model="subForm.inv_points"
              :controls="false"
              :min="1"
              placeholder="请输入积分数量"
              style="width: 200px;"
            />
          </el-form-item>
        </template>
        
        <!-- 被推荐人奖励设置 -->
        <template v-if="checkList.includes('被推荐人')">
          <el-form-item label="被推荐人奖励类型" prop="rec_reward_type">
            <el-radio-group v-model="subForm.rec_reward_type">
              <el-radio :value="1">卡券</el-radio>
              <el-radio :value="2">积分</el-radio>
            </el-radio-group>
          </el-form-item>
          
          <el-form-item label="被推荐人奖励" v-if="subForm.rec_reward_type === 1">
            <LbButton type="primary" @click="showRecCouponDialog = true">
              选择卡券
            </LbButton>
            <div v-if="selectedRecCoupons.length > 0" style="margin-top: 8px;">
              <el-tag 
                v-for="coupon in selectedRecCoupons" 
                :key="coupon.id"
                closable
                @close="removeRecCoupon(coupon.id)"
                style="margin-right: 8px; margin-bottom: 8px;"
              >
                {{ coupon.title }}
              </el-tag>
            </div>
          </el-form-item>
          
          <el-form-item label="被推荐人积分" v-if="subForm.rec_reward_type === 2">
            <el-input-number
              v-model="subForm.rec_points"
              :controls="false"
              :min="1"
              placeholder="请输入积分数量"
              style="width: 200px;"
            />
          </el-form-item>
        </template>
        
        <el-form-item>
          <LbButton 
            type="primary" 
            @click="submitForm" 
            :loading="submitLoading"
            size="default"
          >
            保存活动设置
          </LbButton>
          <LbButton @click="resetForm" style="margin-left: 12px;" size="default">
            重置
          </LbButton>
        </el-form-item>
      </el-form>
    </div>
    
    <!-- 选择卡券对话框 -->
    <el-dialog v-model="showCouponDialog" title="选择推荐人奖励卡券" width="60%">
      <el-table 
        :data="couponList" 
        @selection-change="handleCouponSelection"
        ref="couponTableRef"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="id" label="卡券ID" width="80" />
        <el-table-column prop="title" label="卡券名称" />
        <el-table-column prop="discount" label="优惠金额" width="120">
          <template #default="scope">
            ¥{{ scope.row.discount }}
          </template>
        </el-table-column>
      </el-table>
      <template #footer>
        <LbButton @click="showCouponDialog = false">取消</LbButton>
        <LbButton type="primary" @click="confirmCouponSelection">确定</LbButton>
      </template>
    </el-dialog>
    
    <!-- 选择被推荐人卡券对话框 -->
    <el-dialog v-model="showRecCouponDialog" title="选择被推荐人奖励卡券" width="60%">
      <el-table 
        :data="couponList" 
        @selection-change="handleRecCouponSelection"
        ref="recCouponTableRef"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="id" label="卡券ID" width="80" />
        <el-table-column prop="title" label="卡券名称" />
        <el-table-column prop="discount" label="优惠金额" width="120">
          <template #default="scope">
            ¥{{ scope.row.discount }}
          </template>
        </el-table-column>
      </el-table>
      <template #footer>
        <LbButton @click="showRecCouponDialog = false">取消</LbButton>
        <LbButton type="primary" @click="confirmRecCouponSelection">确定</LbButton>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import TopNav from '@/components/common/TopNav.vue'
import LbButton from '@/components/common/LbButton.vue'
import LbTips from '@/components/common/LbToolTips.vue'
import LbCover from '@/components/common/LbCover.vue'
import LbToolTips from '@/components/common/LbToolTips.vue'

// 响应式数据
const subFormRef = ref()
const couponTableRef = ref()
const recCouponTableRef = ref()
const submitLoading = ref(false)
const showCouponDialog = ref(false)
const showRecCouponDialog = ref(false)
const couponList = ref([])
const selectedCoupons = ref([])
const selectedRecCoupons = ref([])
const tempSelectedCoupons = ref([])
const tempSelectedRecCoupons = ref([])
const checkList = ref([])

// 奖励人员选项
const authList = ref([
  {
    title: '推荐人',
    is_check: false,
    tips: '邀请好友成功后获得奖励'
  },
  {
    title: '被推荐人',
    is_check: false,
    tips: '被邀请并成功注册后获得奖励'
  }
])

// 表单数据
const subForm = reactive({
  status: 0,
  share_img: '',
  time: [],
  inv_user_num: 1,
  inv_time: 24,
  atv_num: 1,
  inv_user: [],
  inv_reward_type: 1,
  inv_points: 0,
  inv_coupon_ids: [],
  rec_reward_type: 1,
  rec_points: 0,
  rec_coupon_ids: []
})

// 表单验证规则
const subFormRules = {
  time: [
    { required: true, message: '请选择活动时间', trigger: 'change' }
  ],
  inv_user_num: [
    { required: true, message: '请输入邀请好友数', trigger: 'blur' }
  ],
  inv_time: [
    { required: true, message: '请输入邀请有效期', trigger: 'blur' }
  ],
  atv_num: [
    { required: true, message: '请输入发起活动次数', trigger: 'blur' }
  ]
}

// 方法
const disabledDate = (time) => {
  return time.getTime() < Date.now() - 8.64e7
}

const getCover = (files) => {
  if (files && files.length > 0) {
    subForm.share_img = files[0].url
  }
}

const changeCheckBox = (checkedList) => {
  subForm.inv_user = checkedList
}

const getCouponList = async () => {
  try {
    const response = await fetch('/api/market/coupon/list')
    const result = await response.json()
    if (result.code === 200) {
      couponList.value = result.data.list || []
    }
  } catch (error) {
    console.error('获取卡券列表失败:', error)
  }
}

const handleCouponSelection = (selection) => {
  tempSelectedCoupons.value = selection
}

const confirmCouponSelection = () => {
  selectedCoupons.value = [...tempSelectedCoupons.value]
  subForm.inv_coupon_ids = selectedCoupons.value.map(coupon => coupon.id)
  showCouponDialog.value = false
}

const removeCoupon = (couponId) => {
  selectedCoupons.value = selectedCoupons.value.filter(coupon => coupon.id !== couponId)
  subForm.inv_coupon_ids = selectedCoupons.value.map(coupon => coupon.id)
}

const handleRecCouponSelection = (selection) => {
  tempSelectedRecCoupons.value = selection
}

const confirmRecCouponSelection = () => {
  selectedRecCoupons.value = [...tempSelectedRecCoupons.value]
  subForm.rec_coupon_ids = selectedRecCoupons.value.map(coupon => coupon.id)
  showRecCouponDialog.value = false
}

const removeRecCoupon = (couponId) => {
  selectedRecCoupons.value = selectedRecCoupons.value.filter(coupon => coupon.id !== couponId)
  subForm.rec_coupon_ids = selectedRecCoupons.value.map(coupon => coupon.id)
}

const getActivityInfo = async () => {
  try {
    const response = await fetch('/api/market/activity/info')
    const result = await response.json()
    if (result.code === 200) {
      Object.assign(subForm, result.data)
      checkList.value = result.data.inv_user || []
      selectedCoupons.value = result.data.inv_coupons || []
      selectedRecCoupons.value = result.data.rec_coupons || []
    }
  } catch (error) {
    console.error('获取活动信息失败:', error)
  }
}

const submitForm = async () => {
  try {
    await subFormRef.value.validate()

    submitLoading.value = true

    const response = await fetch('/api/market/activity/save', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(subForm)
    })

    const result = await response.json()

    if (result.code === 200) {
      ElMessage.success('保存成功')
    } else {
      ElMessage.error(result.message || '保存失败')
    }
  } catch (error) {
    console.error('保存失败:', error)
    ElMessage.error('保存失败')
  } finally {
    submitLoading.value = false
  }
}

const resetForm = () => {
  subFormRef.value.resetFields()
  checkList.value = []
  selectedCoupons.value = []
  selectedRecCoupons.value = []
}

// 生命周期
onMounted(() => {
  getCouponList()
  getActivityInfo()
})
</script>

<style scoped>
.lb-edit-card {
  padding: 20px;
  background-color: #fff;
  min-height: calc(100vh - 60px);
}

.page-main {
  max-width: 800px;
  margin: 0 auto;
}

.basic-form {
  margin-top: 20px;
}

.el-form-item {
  margin-bottom: 22px;
}

.el-tag {
  margin-right: 8px;
  margin-bottom: 8px;
}

@media (max-width: 768px) {
  .lb-edit-card {
    padding: 10px;
  }

  .page-main {
    max-width: 100%;
  }

  .basic-form {
    margin-top: 10px;
  }
}
</style>
