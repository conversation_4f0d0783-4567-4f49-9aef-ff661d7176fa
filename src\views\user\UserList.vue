<!--
  用户管理页面
  功能：用户列表查看、编辑、拉黑/解除拉黑、删除
-->

<template>
  <div class="page-container">
    <TopNav title="用户管理" />

    <div class="content-container">
      <div class="search-form-container">
        <el-form
          ref="searchFormRef"
          :model="searchForm"
          :inline="true"
          class="search-form"
        >
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="用户昵称" prop="nickName">
                <el-input
                  size="default"
                  v-model="searchForm.nickName"
                  placeholder="请输入用户昵称，支持模糊查询"
                  clearable
                  style="width: 200px"
                />
              </el-form-item>

              <el-form-item label="用户状态" prop="status">
                <el-select
                  size="default"
                  v-model="searchForm.status"
                  placeholder="请选择用户状态"
                  clearable
                  style="width: 180px"
                >
                  <el-option label="查看所有" :value="0" />
                  <el-option label="正常用户" :value="1" />
                  <el-option label="黑名单用户" :value="2" />
                </el-select>
              </el-form-item>

              <el-form-item label="加入时间" prop="timeRange">
                <el-date-picker
                  size="default"
                  v-model="timeRange"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                  style="width: 240px"
                />
              </el-form-item>

              <el-form-item label="订单排序" prop="sortOrderCount">
                <el-select
                  size="default"
                  v-model="searchForm.sortOrderCount"
                  placeholder="请选择排序方式"
                  clearable
                  style="width: 150px"
                >
                  <el-option label="不排序" :value="-1" />
                  <el-option label="降序" :value="0" />
                  <el-option label="升序" :value="1" />
                </el-select>
              </el-form-item>

              <el-form-item>
                <LbButton
                  size="default"
                  type="primary"
                  icon="Search"
                  @click="handleSearch"
                  :loading="loading"
                >
                  搜索
                </LbButton>
                <LbButton
                  size="default"
                  icon="RefreshLeft"
                  @click="handleReset"
                >
                  重置
                </LbButton>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>

      <div class="table-container">
        <el-table
          v-loading="loading"
          :data="tableData"
          :header-cell-style="{
            background: '#f5f7fa',
            color: '#606266',
            fontSize: '16px',
            fontWeight: '600'
          }"
          :cell-style="{
            fontSize: '14px',
            padding: '12px 8px'
          }"
          style="width: 100%"
          :default-expand-all="false"
          table-layout="auto"
        >
          <el-table-column prop="id" label="用户ID" width="100" align="center" />

          <el-table-column prop="avatarUrl" label="头像" width="200" align="center">
            <template #default="{ row }">
              <el-avatar
                v-if="row.avatarUrl"
                :src="row.avatarUrl"
                :size="40"
                @error="handleAvatarError"
              />
              <el-avatar v-else :size="40" icon="User" />
            </template>
          </el-table-column>
          <el-table-column prop="nickName" label="用户昵称" width="150" show-overflow-tooltip />
          <el-table-column prop="status" label="用户状态" min-width="320" align="center">
            <template #default="{ row }">
              <el-tag :type="getStatusTagType(row.status)">
                {{ getStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="createTime" label="加入时间" width="180" show-overflow-tooltip />
          <el-table-column prop="orderCount" label="订单总量" width="100" align="center">
            <template #default="{ row }">
              {{ row.orderCount || 0 }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="280" align="center" fixed="right">
            <template #default="{ row }">
              <div class="table-operate-buttons">
                <LbButton
                  size="default"
                  type="primary"
                  @click="handleEdit(row)"
                >
                  编辑
                </LbButton>
                <LbButton
                  v-if="row.status === 1"
                  size="default"
                  type="danger"
                  @click="handleBlacklist(row, 1)"
                >
                  拉黑
                </LbButton>
                <LbButton
                  v-if="row.status === 2"
                  size="default"
                  type="success"
                  @click="handleBlacklist(row, 0)"
                >
                  解除拉黑
                </LbButton>
                <LbButton
                  size="default"
                  type="danger"
                  @click="handleDelete(row)"
                >
                  删除
                </LbButton>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <LbPage
        :page="searchForm.pageNum"
        :page-size="searchForm.pageSize"
        :total="total"
        @handleSizeChange="handleSizeChange"
        @handleCurrentChange="handleCurrentChange"
      />
    </div>

    <el-dialog
      v-model="editDialogVisible"
      title="编辑用户"
      width="600px"
      :close-on-click-modal="false"
      @close="resetEditForm"
    >
      <el-form
        ref="editFormRef"
        :model="editForm"
        :rules="editRules"
        label-width="100px"
      >
        <el-form-item label="用户昵称" prop="nickName">
          <el-input
            v-model="editForm.nickName"
            placeholder="请输入用户昵称"
            maxlength="50"
            show-word-limit
          />
        </el-form-item>
        <el-form-item label="用户头像" prop="avatarUrl">
          <el-upload
            class="avatar-upload"
            action="#"
            :auto-upload="false"
            :on-change="handleAvatarChange"
            :on-remove="handleAvatarRemove"
            :before-upload="beforeAvatarUpload"
            :file-list="avatarFileList"
            list-type="picture-card"
            :limit="1"
            accept="image/*"
          >
            <el-icon><Plus /></el-icon>
            <template #tip>
              <div class="el-upload__tip">
                只能上传jpg/png等图片文件，且不超过2MB
              </div>
            </template>
          </el-upload>
          <div v-if="avatarUploadProgress > 0 && avatarUploadProgress < 100" class="upload-progress">
            <el-progress :percentage="avatarUploadProgress" :show-text="true" />
            <p>上传中... {{ avatarUploadProgress }}%</p>
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <LbButton @click="editDialogVisible = false">取消</LbButton>
          <LbButton
            type="primary"
            @click="handleEditSubmit"
            :loading="editLoading"
          >
            确定
          </LbButton>
        </span>
      </template>
    </el-dialog>

    <el-dialog
      v-model="blacklistDialogVisible"
      :title="blacklistAction === 1 ? '加入黑名单' : '移除黑名单'"
      width="500px"
      :close-on-click-modal="false"
      @close="blacklistForm.text = ''"
    >
      <el-form
        ref="blacklistFormRef"
        :model="blacklistForm"
        :rules="blacklistRules"
        label-width="100px"
      >
        <el-form-item label="用户昵称">
          <span>{{ currentUser?.nickName }}</span>
        </el-form-item>
        <el-form-item label="操作原因" prop="text">
          <el-input
            v-model="blacklistForm.text"
            :placeholder="blacklistAction === 1 ? '请输入拉黑原因' : '请输入解除拉黑原因'"
            type="textarea"
            :rows="4"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <LbButton @click="blacklistDialogVisible = false">取消</LbButton>
          <LbButton
            :type="blacklistAction === 1 ? 'danger' : 'success'"
            @click="handleBlacklistSubmit"
            :loading="blacklistLoading"
          >
            {{ blacklistAction === 1 ? '确认拉黑' : '确认解除' }}
          </LbButton>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, getCurrentInstance } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import TopNav from '@/components/common/TopNav.vue'
import LbButton from '@/components/common/LbButton.vue'
import LbPage from '@/components/common/LbPage.vue'

const { proxy } = getCurrentInstance()

// 响应式数据
const loading = ref(false)
const editLoading = ref(false)
const blacklistLoading = ref(false)
const tableData = ref([])
const total = ref(0)
const timeRange = ref([])

// 头像上传相关
const avatarFileList = ref([])
const avatarUploadProgress = ref(0)
const avatarUploading = ref(false)

// 搜索表单
const searchForm = reactive({
  pageNum: 1,
  pageSize: 10,
  nickName: '',
  status: 0,
  beginTime: '',
  endTime: '',
  sortOrderCount: -1
})

// 搜索表单引用
const searchFormRef = ref(null)

// 编辑对话框相关
const editDialogVisible = ref(false)
const editFormRef = ref(null)
const editForm = reactive({
  id: null,
  nickName: '',
  avatarUrl: ''
})

const editRules = {
  nickName: [
    { required: true, message: '请输入用户昵称', trigger: 'blur' },
    { min: 1, max: 50, message: '昵称长度在 1 到 50 个字符', trigger: 'blur' }
  ]
}

// 黑名单操作对话框相关
const blacklistDialogVisible = ref(false)
const blacklistFormRef = ref(null)
const blacklistAction = ref(1) // 1: 加入黑名单, 0: 移除黑名单
const currentUser = ref(null)
const blacklistForm = reactive({
  text: ''
})

const blacklistRules = {
  text: [
    { required: true, message: '请输入操作原因', trigger: 'blur' },
    { min: 1, max: 200, message: '原因长度在 1 到 200 个字符', trigger: 'blur' }
  ]
}

/**
 * 获取状态文本
 */
const getStatusText = (status) => {
  const statusMap = {
    1: '正常用户',
    2: '黑名单用户'
  }
  return statusMap[status] || '未知'
}

/**
 * 获取状态标签类型
 */
const getStatusTagType = (status) => {
  const typeMap = {
    1: 'success',
    2: 'danger'
  }
  return typeMap[status] || 'info'
}

/**
 * 头像加载错误处理
 */
const handleAvatarError = () => {
  // 头像加载失败时的处理逻辑
  console.log('头像加载失败')
}

/**
 * 加载用户列表
 */
const loadUserList = async () => {
  try {
    loading.value = true
    console.log('🔍 开始加载用户列表，参数:', searchForm)

    // 处理时间范围
    if (timeRange.value && timeRange.value.length === 2) {
      searchForm.beginTime = timeRange.value[0]
      searchForm.endTime = timeRange.value[1]
    } else {
      searchForm.beginTime = ''
      searchForm.endTime = ''
    }

    const response = await proxy.$api.user.userList(searchForm)
    console.log('📋 用户列表响应:', response)

    if (response.code === '200') {
      tableData.value = response.data.list || []
      total.value = response.data.totalCount || 0

      console.log(`✅ 用户列表加载成功，共 ${total.value} 条数据`)
    } else {
      ElMessage.error(response.msg || '获取用户列表失败')
    }
  } catch (error) {
    console.error('❌ 加载用户列表失败:', error)
    ElMessage.error('获取用户列表失败')
  } finally {
    loading.value = false
  }
}

/**
 * 搜索处理
 */
const handleSearch = () => {
  searchForm.pageNum = 1
  loadUserList()
}

/**
 * 重置搜索
 */
const handleReset = () => {
  searchFormRef.value?.resetFields()
  timeRange.value = []
  Object.assign(searchForm, {
    pageNum: 1,
    pageSize: 10,
    nickName: '',
    status: 0,
    beginTime: '',
    endTime: '',
    sortOrderCount: -1
  })
  loadUserList()
}

/**
 * 分页大小变化
 */
const handleSizeChange = (size) => {
  searchForm.pageSize = size
  searchForm.pageNum = 1
  loadUserList()
}

/**
 * 当前页变化
 */
const handleCurrentChange = (page) => {
  searchForm.pageNum = page
  loadUserList()
}

/**
 * 编辑用户
 */
const handleEdit = (row) => {
  currentUser.value = row
  editForm.id = row.id
  editForm.nickName = row.nickName
  editForm.avatarUrl = row.avatarUrl || ''

  // 如果有头像，设置文件列表用于显示
  if (row.avatarUrl) {
    avatarFileList.value = [{
      name: 'avatar',
      url: row.avatarUrl
    }]
  } else {
    avatarFileList.value = []
  }

  editDialogVisible.value = true
}

/**
 * 提交编辑
 */
const handleEditSubmit = async () => {
  try {
    await editFormRef.value?.validate()

    editLoading.value = true
    console.log('✏️ 提交编辑用户:', editForm)

    const response = await proxy.$api.user.userUpdate(editForm)
    console.log('📝 编辑用户响应:', response)

    if (response.code === '200') {
      ElMessage.success('编辑用户成功')
      editDialogVisible.value = false
      loadUserList()
    } else {
      ElMessage.error(response.msg || '编辑用户失败')
    }
  } catch (error) {
    console.error('❌ 编辑用户失败:', error)
    ElMessage.error('编辑用户失败')
  } finally {
    editLoading.value = false
  }
}

/**
 * 黑名单操作
 */
const handleBlacklist = (row, action) => {
  currentUser.value = row
  blacklistAction.value = action
  blacklistForm.text = '' // Reset reason when opening
  blacklistDialogVisible.value = true
}

/**
 * 提交黑名单操作
 */
const handleBlacklistSubmit = async () => {
  try {
    await blacklistFormRef.value?.validate()

    blacklistLoading.value = true

    const params = {
      id: currentUser.value.id,
      text: blacklistForm.text,
      status: blacklistAction.value
    }

    console.log('🚫 提交黑名单操作:', params)

    const response = await proxy.$api.user.userAddBlack(params)
    console.log('📝 黑名单操作响应:', response)

    if (response.code === '200') {
      const actionText = blacklistAction.value === 1 ? '加入黑名单' : '移除黑名单'
      ElMessage.success(`${actionText}成功`)
      blacklistDialogVisible.value = false
      loadUserList()
    } else {
      ElMessage.error(response.msg || '操作失败')
    }
  } catch (error) {
    console.error('❌ 黑名单操作失败:', error)
    ElMessage.error('操作失败')
  } finally {
    blacklistLoading.value = false
  }
}

/**
 * 删除用户
 */
const handleDelete = async (row) => {
  try {
    // 确认删除操作
    await ElMessageBox.confirm(
      `确定要删除用户"${row.nickName}"吗？删除后无法恢复！`,
      '删除确认',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning',
        confirmButtonClass: 'el-button--danger'
      }
    )

    console.log('🗑️ 开始删除用户:', row)

    // 调用用户状态变更API，传递status: -1表示删除
    const params = {
      id: row.id,
      status: -1
    }

    console.log('🗑️ 删除用户参数:', params)

    const response = await proxy.$api.user.userStatus(params)
    console.log('📝 删除用户响应:', response)

    if (response.code === '200') {
      ElMessage.success('删除用户成功')
      // 重新加载用户列表
      loadUserList()
    } else {
      ElMessage.error(response.msg || '删除用户失败')
    }

  } catch (error) {
    // 用户取消删除操作
    if (error === 'cancel') {
      console.log('👤 用户取消删除操作')
      return
    }

    console.error('❌ 删除用户失败:', error)
    ElMessage.error('删除用户失败')
  }
}

/**
 * 头像上传前的验证
 */
const beforeAvatarUpload = (file) => {
  console.log('📋 头像上传前验证:', file)

  // 检查文件类型
  const isImage = file.type.indexOf('image/') === 0
  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return false
  }

  // 检查文件大小
  const isLt2M = file.size / 1024 / 1024 < 2
  if (!isLt2M) {
    ElMessage.error('上传头像图片大小不能超过 2MB!')
    return false
  }

  console.log('✅ 头像验证通过')
  return true
}

/**
 * 头像文件变更处理
 */
const handleAvatarChange = async (file, fileList) => {
  console.log('🖼️ 头像文件变更:', file, fileList)

  // Clear previous file if a new one is selected
  if (fileList.length > 1) {
    avatarFileList.value = [file]
  }

  if (file.status === 'ready' && !avatarUploading.value) {
    // 文件准备上传，开始上传流程
    await uploadAvatar(file)
  }
}

/**
 * 头像移除处理
 */
const handleAvatarRemove = (file) => {
  console.log('🗑️ 移除头像:', file)
  editForm.avatarUrl = ''
  avatarUploadProgress.value = 0
  // Clear fileList completely
  avatarFileList.value = []
}

/**
 * 执行头像上传
 */
const uploadAvatar = async (file) => {
  console.log('📤 开始上传头像:', file)

  try {
    avatarUploading.value = true
    avatarUploadProgress.value = 0

    // 创建FormData
    const formData = new FormData()
    formData.append('multipartFile', file.raw)

    console.log('📦 FormData创建完成:', formData)

    // Call upload API
    const result = await proxy.$api.upload.uploadFile(formData, (progressEvent) => {
      // Update upload progress
      avatarUploadProgress.value = Math.round((progressEvent.loaded * 100) / progressEvent.total)
      console.log('📊 上传进度:', avatarUploadProgress.value + '%')
    })

    console.log('✅ 头像上传成功:', result)

    if (result.code === 200 || result.code === '200') {
      // Upload successful, save file URL to form
      editForm.avatarUrl = result.data.url || result.data.fileUrl || result.data
      ElMessage.success('头像上传成功')

      // Update file list display
      avatarFileList.value = [{
        name: file.name,
        url: editForm.avatarUrl,
        status: 'success'
      }]

      console.log('💾 头像URL已保存到表单:', editForm.avatarUrl)
    } else {
      throw new Error(result.message || result.msg || '上传失败')
    }
  } catch (error) {
    console.error('❌ 头像上传失败:', error)
    ElMessage.error('头像上传失败: ' + (error.message || '未知错误'))

    // Clean up failed files
    avatarFileList.value = []
    editForm.avatarUrl = ''
  } finally {
    avatarUploading.value = false
    avatarUploadProgress.value = 0
  }
}

/**
 * 重置编辑表单（关闭弹窗时调用）
 */
const resetEditForm = () => {
  editFormRef.value?.resetFields()
  editForm.id = null
  editForm.nickName = ''
  editForm.avatarUrl = ''
  avatarFileList.value = []
  avatarUploadProgress.value = 0
  avatarUploading.value = false
}

// 组件挂载时加载数据
onMounted(() => {
  console.log('🚀 用户管理页面初始化')
  loadUserList()
})
</script>

<style scoped>
/* ===== 核心样式实现 ===== */

/* 1. 页面容器 - 基础布局 */
.page-container {
  padding: 0px;
}

/* 2. 内容容器 - 白色背景 + 圆角 + 阴影 */
.content-container {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* 3. 搜索表单 - 灰色背景区域 */
.search-form-container {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
}

.search-form .el-form-item {
  margin-bottom: 0;
  margin-right: 20px;
}

.search-form .el-form-item__label {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

/* 4. 表格容器 - 关键的阴影和圆角效果 */
.table-container {
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

/* 5. 表格样式 - 深度选择器覆盖Element Plus默认样式 */
:deep(.el-table) {
  border-left: 1px solid #ebeef5;
  border-right: 1px solid #ebeef5;
}

/* 表头样式 */
.el-table .el-table__header-wrapper th {
  background-color: #f5f7fa !important;
  color: #606266 !important;
  font-size: 16px !important;
  font-weight: 600 !important;
  padding: 15px 8px !important;
}

/* 表格内容样式 */
.el-table .el-table__body-wrapper td {
  font-size: 14px !important;
  padding: 12px 8px !important;
  color: #333 !important;
}

/* 行悬停效果 */
.el-table .el-table__row:hover {
  background-color: #f8f9fa !important;
}

/* 操作按钮容器，使按钮水平排列并有间距 */
.table-operate-buttons {
  display: flex;
  justify-content: center; /* Center buttons horizontally */
  gap: 8px; /* Space between buttons */
}

/* 6. 按钮样式优化 */
.el-button {
  font-size: 14px;
  padding: 8px 16px;
}

.el-button + .el-button {
  margin-left: 0; /* Remove default margin-left from Element Plus */
}

/* 确保按钮文字正常显示 */
.table-operate-buttons .el-button {
  min-width: auto;
  white-space: nowrap;
}

/* 删除按钮测试样式 */
.delete-btn-test {
  font-size: 14px !important;
  padding: 8px 16px !important;
  min-width: 60px !important;
}

.delete-btn-test span {
  display: inline-block !important;
  visibility: visible !important;
  opacity: 1 !important;
  color: inherit !important;
}

/* 7. 标签样式优化 */
.el-tag {
  font-size: 12px;
}

/* 8. 对话框样式 */
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

/* 9. 头像上传组件样式 */
.avatar-upload :deep(.el-upload--picture-card) {
  width: 120px;
  height: 120px;
  border: 2px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: border-color 0.3s;
}

.avatar-upload :deep(.el-upload--picture-card:hover) {
  border-color: #409eff;
}

.avatar-upload :deep(.el-upload-list--picture-card .el-upload-list__item) {
  width: 120px;
  height: 120px;
  border-radius: 6px;
}

.avatar-upload :deep(.el-upload__tip) {
  font-size: 12px;
  color: #606266;
  margin-top: 8px;
  line-height: 1.4;
}

/* 上传进度样式 */
.upload-progress {
  margin-top: 10px;
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
}

.upload-progress p {
  margin: 5px 0 0 0;
  font-size: 12px;
  color: #606266;
  text-align: center;
}

/* 10. 响应式设计 */
@media (max-width: 768px) {
  .content-container {
    padding: 10px;
  }

  .search-form-container {
    padding: 12px;
  }

  .search-form .el-form-item {
    margin-right: 10px;
  }
}

/* 11. 加载状态优化 */
:deep(.el-loading-mask) {
  border-radius: 8px;
}
</style>
```