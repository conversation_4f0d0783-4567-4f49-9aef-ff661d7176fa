/**
 * 路由守卫配置

 * 
 * 功能：
 * - 登录状态验证
 * - 权限控制
 * - 动态路由生成
 * - 页面标题设置
 * - 进度条控制
 */

import NProgress from 'nprogress'
import { ElMessage } from 'element-plus'
import store from '@/store'
import { getToken } from '@/utils/auth'

// 白名单路由（不需要登录）
const whiteList = ['/login', '/register', '/forgot-password', '/404', '/403', '/500']

// 设置路由守卫
export function setupRouterGuards(router) {
  // 全局前置守卫
  router.beforeEach(async (to, from, next) => {
    console.log(`🚀 路由守卫执行: ${from.path} -> ${to.path}`)
    console.log('🔍 目标路由信息:', { path: to.path, name: to.name, matched: to.matched.length })

    // 开始进度条
    NProgress.start()

    // 设置页面标题
    document.title = getPageTitle(to.meta.title)

    // 获取token
    const hasToken = getToken()
    console.log('🔑 Token状态:', hasToken ? '存在' : '不存在')

    if (hasToken) {
      // 已登录
      if (to.path === '/login') {
        // 如果已登录，重定向到服务项目列表页
        next({ path: '/service/list' })
        NProgress.done()
      } else {
        // 登录成功后直接进入后台，跳过用户信息获取
        console.log('🔐 用户已登录，直接进入后台')

        // 设置默认用户信息（避免其他地方的依赖问题）
        const hasUserInfo = store.getters['auth/userInfo']?.id
        if (!hasUserInfo) {
          // 设置默认用户信息
          store.commit('auth/SET_USER_INFO', {
            id: 1,
            username: 'admin',
            name: '管理员',
            avatar: '',
            email: '<EMAIL>',
            phone: '13800138000'
          })

          // 设置默认角色和权限
          store.commit('auth/SET_ROLES', ['admin'])
          store.commit('auth/SET_PERMISSIONS', ['*:*:*'])
        }

        // 获取用户角色（使用默认角色）
        const userRoles = store.getters['auth/roles'] || ['admin']

        // 检查菜单数据是否已加载（登录时应该已经设置）
        const menuLoaded = store.getters['menu/menuLoaded']
        if (!menuLoaded) {
          console.log('⚠️ 菜单数据未加载，使用降级菜单')
          try {
            // 不再调用 api/admin/role/menus，直接使用降级菜单
            await store.dispatch('menu/useFallbackMenus')
            console.log('🌲 降级菜单加载成功')
          } catch (error) {
            console.warn('⚠️ 降级菜单加载失败:', error)
          }
        } else {
          console.log('✅ 菜单数据已存在，跳过加载')
        }

        // 生成动态路由（每次都重新生成，确保路由正确添加）
        console.log('🔄 开始生成动态路由，用户角色:', userRoles)
        const accessRoutes = await store.dispatch('routes/generateRoutes', userRoles)
        console.log('🛣️ 生成的动态路由数量:', accessRoutes.length)
        console.log('🛣️ 生成的动态路由:', accessRoutes.map(r => ({ path: r.path, name: r.name })))

        // 动态添加路由
        accessRoutes.forEach(route => {
          try {
            router.addRoute(route)
            console.log(`✅ 成功添加路由: ${route.path} (${route.name})`)
          } catch (error) {
            console.error(`❌ 添加路由失败: ${route.path} (${route.name})`, error)
          }
        })

        // 验证路由是否添加成功
        console.log('🔍 当前所有路由:', router.getRoutes().map(r => ({ path: r.path, name: r.name })))

        // 直接放行，进入后台
        next()
      }
    } else {
      // 未登录
      if (whiteList.includes(to.path)) {
        // 在白名单中，直接放行
        next()
      } else {
        // 不在白名单中，重定向到登录页
        next(`/login?redirect=${to.path}`)
        NProgress.done()
      }
    }
  })

  // 全局后置守卫
  router.afterEach((to, from) => {
    // 结束进度条
    NProgress.done()

    // 更新面包屑
    store.dispatch('ui/generateBreadcrumb', to)

    // 添加到访问历史
    store.dispatch('ui/addVisitedView', to)
  })

  // 路由错误处理
  router.onError((error) => {
    console.error('路由错误:', error)
    NProgress.done()
    
    // 可以在这里添加错误上报逻辑
    if (import.meta.env.PROD) {
      // 生产环境错误上报
      // errorMonitor.captureException(error)
    }
  })
}

// 获取页面标题
function getPageTitle(pageTitle) {
  const title = '今师傅'
  if (pageTitle) {
    return `${pageTitle} - ${title}`
  }
  return title
}

// 权限验证函数
export function hasPermission(roles, permissionRoles) {
  if (!permissionRoles) return true
  if (!roles || roles.length === 0) return false
  
  return roles.some(role => permissionRoles.includes(role))
}

// 检查路由权限
export function checkRoutePermission(route, userRoles) {
  if (!route.meta || !route.meta.roles) {
    return true
  }
  
  return hasPermission(userRoles, route.meta.roles)
}

// 过滤异步路由
export function filterAsyncRoutes(routes, roles) {
  const res = []

  routes.forEach(route => {
    const tmp = { ...route }
    
    if (checkRoutePermission(tmp, roles)) {
      if (tmp.children) {
        tmp.children = filterAsyncRoutes(tmp.children, roles)
      }
      res.push(tmp)
    }
  })

  return res
}
