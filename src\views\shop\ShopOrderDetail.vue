<!--
  订单详情页面
 /src/view/shop/order/detail.vue重构
-->

<template>
  <div class="lb-order-detail">
    <TopNav title="订单详情" :isBack="true" />
    <div class="page-main">
      <el-card v-loading="loading">
        <!-- 订单基本信息 -->
        <div class="order-header">
          <h3>订单信息</h3>
          <div class="order-status">
            <el-tag :type="getStatusType(orderInfo.pay_type)" size="large">
              {{ getStatusText(orderInfo.pay_type) }}
            </el-tag>
          </div>
        </div>
        
        <el-descriptions :column="2" border>
          <el-descriptions-item label="订单号">{{ orderInfo.order_code }}</el-descriptions-item>
          <el-descriptions-item label="下单时间">{{ orderInfo.create_time }}</el-descriptions-item>
          <el-descriptions-item label="下单人">{{ orderInfo.user_name }}</el-descriptions-item>
          <el-descriptions-item label="联系电话">{{ orderInfo.user_mobile }}</el-descriptions-item>
          <el-descriptions-item label="服务地址">{{ orderInfo.address_info }}</el-descriptions-item>
          <el-descriptions-item label="预约时间">{{ orderInfo.appointment_time }}</el-descriptions-item>
        </el-descriptions>
        
        <!-- 项目信息 -->
        <div class="section-title">项目信息</div>
        <el-table :data="orderInfo.order_goods" style="width: 100%">
          <el-table-column label="项目图片" width="100">
            <template #default="scope">
              <img :src="scope.row.goods_cover" alt="项目图片" style="width: 60px; height: 60px; border-radius: 4px;" />
            </template>
          </el-table-column>
          <el-table-column prop="goods_name" label="项目名称" />
          <el-table-column prop="goods_price" label="单价" width="120">
            <template #default="scope">
              ¥{{ scope.row.goods_price }}
            </template>
          </el-table-column>
          <el-table-column prop="num" label="数量" width="80" />
          <el-table-column label="小计" width="120">
            <template #default="scope">
              ¥{{ (scope.row.goods_price * scope.row.num).toFixed(2) }}
            </template>
          </el-table-column>
        </el-table>
        
        <!-- 费用明细 -->
        <div class="section-title">费用明细</div>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="商品总额">¥{{ orderInfo.goods_price }}</el-descriptions-item>
          <el-descriptions-item label="优惠金额">-¥{{ orderInfo.discount_price || 0 }}</el-descriptions-item>
          <el-descriptions-item label="实付金额">
            <span style="color: #e6a23c; font-weight: 600; font-size: 16px;">¥{{ orderInfo.pay_price }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="支付方式">{{ getPayMethodText(orderInfo.pay_method) }}</el-descriptions-item>
        </el-descriptions>
        
        <!-- 师傅信息 -->
        <div class="section-title" v-if="orderInfo.coach_info">师傅信息</div>
        <el-descriptions :column="2" border v-if="orderInfo.coach_info">
          <el-descriptions-item label="师傅姓名">{{ orderInfo.coach_info.coach_name }}</el-descriptions-item>
          <el-descriptions-item label="联系电话">{{ orderInfo.coach_info.mobile }}</el-descriptions-item>
          <el-descriptions-item label="师傅等级">{{ orderInfo.coach_info.level_name }}</el-descriptions-item>
          <el-descriptions-item label="从业年份">{{ orderInfo.coach_info.work_time }}年</el-descriptions-item>
        </el-descriptions>
        
        <!-- 服务记录 -->
        <div class="section-title" v-if="orderInfo.service_logs && orderInfo.service_logs.length > 0">服务记录</div>
        <el-timeline v-if="orderInfo.service_logs && orderInfo.service_logs.length > 0">
          <el-timeline-item
            v-for="(log, index) in orderInfo.service_logs"
            :key="index"
            :timestamp="log.create_time"
            placement="top"
          >
            <el-card>
              <h4>{{ log.title }}</h4>
              <p>{{ log.content }}</p>
              <div v-if="log.images && log.images.length > 0" class="service-images">
                <img 
                  v-for="(img, imgIndex) in log.images" 
                  :key="imgIndex"
                  :src="img" 
                  alt="服务图片"
                  style="width: 100px; height: 100px; margin-right: 8px; border-radius: 4px;"
                />
              </div>
            </el-card>
          </el-timeline-item>
        </el-timeline>
        
        <!-- 操作按钮 -->
        <div class="action-buttons">
          <LbButton 
            v-if="orderInfo.pay_type === 1" 
            type="warning" 
            @click="cancelOrder"
          >
            取消订单
          </LbButton>
          <LbButton 
            v-if="orderInfo.pay_type === 2" 
            type="primary" 
            @click="assignTechnician"
          >
            分配师傅
          </LbButton>
          <LbButton 
            v-if="orderInfo.pay_type === 4" 
            type="success" 
            @click="completeOrder"
          >
            完成订单
          </LbButton>
          <LbButton @click="$router.go(-1)">
            返回列表
          </LbButton>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import TopNav from '@/components/common/TopNav.vue'
import LbButton from '@/components/common/LbButton.vue'

const route = useRoute()
const router = useRouter()

// 响应式数据
const loading = ref(false)
const orderInfo = reactive({
  id: null,
  order_code: '',
  user_name: '',
  user_mobile: '',
  address_info: '',
  appointment_time: '',
  create_time: '',
  pay_type: 1,
  pay_method: 1,
  goods_price: 0,
  discount_price: 0,
  pay_price: 0,
  order_goods: [],
  coach_info: null,
  service_logs: []
})

// 方法
const getOrderDetail = async (id) => {
  loading.value = true
  try {
    const response = await fetch(`/api/shop/order/detail/${id}`)
    const result = await response.json()
    
    if (result.code === 200) {
      Object.assign(orderInfo, result.data)
    } else {
      ElMessage.error(result.message || '获取订单详情失败')
    }
  } catch (error) {
    console.error('获取订单详情失败:', error)
    ElMessage.error('获取订单详情失败')
  } finally {
    loading.value = false
  }
}

const getStatusType = (payType) => {
  const statusMap = {
    1: 'warning',  // 待付款
    2: 'info',     // 待接单
    3: 'primary',  // 待服务
    4: 'success',  // 服务中
    5: 'warning',  // 待评价
    6: 'success',  // 已完成
    7: 'danger'    // 已取消
  }
  return statusMap[payType] || 'info'
}

const getStatusText = (payType) => {
  const statusMap = {
    1: '待付款',
    2: '待接单',
    3: '待服务',
    4: '服务中',
    5: '待评价',
    6: '已完成',
    7: '已取消'
  }
  return statusMap[payType] || '未知'
}

const getPayMethodText = (payMethod) => {
  const methodMap = {
    1: '微信支付',
    2: '支付宝',
    3: '余额支付'
  }
  return methodMap[payMethod] || '未知'
}

const cancelOrder = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要取消订单 "${orderInfo.order_code}" 吗？`,
      '取消订单确认',
      {
        confirmButtonText: '确定取消',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const response = await fetch(`/api/shop/order/cancel/${orderInfo.id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ reason: '管理员取消' })
    })
    
    const result = await response.json()

    if (result.code === 200) {
      ElMessage.success('订单取消成功')
      getOrderDetail(orderInfo.id)
    } else {
      ElMessage.error(result.message || '取消失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('取消订单失败:', error)
      ElMessage.error('取消失败')
    }
  }
}

const assignTechnician = () => {
  router.push(`/shop/order/assign?id=${orderInfo.id}`)
}

const completeOrder = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要完成订单 "${orderInfo.order_code}" 吗？`,
      '完成订单确认',
      {
        confirmButtonText: '确定完成',
        cancelButtonText: '取消',
        type: 'success'
      }
    )

    const response = await fetch(`/api/shop/order/complete/${orderInfo.id}`, {
      method: 'PUT'
    })
    
    const result = await response.json()

    if (result.code === 200) {
      ElMessage.success('订单完成成功')
      getOrderDetail(orderInfo.id)
    } else {
      ElMessage.error(result.message || '完成失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('完成订单失败:', error)
      ElMessage.error('完成失败')
    }
  }
}

// 生命周期
onMounted(() => {
  if (route.query.id) {
    getOrderDetail(route.query.id)
  }
})
</script>

<style scoped>
.lb-order-detail {
  padding: 20px;
  background-color: #fff;
  min-height: calc(100vh - 60px);
}

.page-main {
  max-width: 1200px;
  margin: 0 auto;
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.order-header h3 {
  margin: 0;
  color: #303133;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin: 30px 0 15px 0;
  padding-bottom: 8px;
  border-bottom: 2px solid #409eff;
}

.service-images {
  margin-top: 10px;
}

.action-buttons {
  margin-top: 30px;
  text-align: center;
}

.action-buttons .el-button {
  margin: 0 8px;
}

@media (max-width: 768px) {
  .lb-order-detail {
    padding: 10px;
  }

  .order-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .action-buttons {
    text-align: left;
  }

  .action-buttons .el-button {
    margin: 4px;
    width: calc(50% - 8px);
  }
}
</style>
