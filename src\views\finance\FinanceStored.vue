<!--
  储值管理页面
 /src/view/finance/stored/list.vue重构
-->

<template>
  <div class="lb-finance-stored">
    <TopNav />
    <div class="page-main">
      <!-- 统计卡片 -->
      <el-row :gutter="20" class="stats-cards">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-value">{{ stats.total_users || 0 }}</div>
              <div class="stat-label">储值用户</div>
            </div>
            <div class="stats-icon users">
              <i class="el-icon-user"></i>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-value">¥{{ stats.total_amount || 0 }}</div>
              <div class="stat-label">储值总额</div>
            </div>
            <div class="stats-icon amount">
              <i class="el-icon-money"></i>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-value">¥{{ stats.used_amount || 0 }}</div>
              <div class="stat-label">已消费</div>
            </div>
            <div class="stats-icon used">
              <i class="el-icon-shopping-cart-2"></i>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-value">¥{{ stats.balance_amount || 0 }}</div>
              <div class="stat-label">余额</div>
            </div>
            <div class="stats-icon balance">
              <i class="el-icon-wallet"></i>
            </div>
          </el-card>
        </el-col>
      </el-row>
      
      <!-- 搜索表单 -->
      <el-row class="page-search-form">
        <el-form @submit.prevent :inline="true" :model="searchForm" ref="searchFormRef">
          <el-form-item label="用户昵称" prop="nickName">
            <el-input v-model="searchForm.nickName" placeholder="请输入用户昵称" style="width: 200px;"></el-input>
          </el-form-item>
          <el-form-item label="手机号" prop="mobile">
            <el-input v-model="searchForm.mobile" placeholder="请输入手机号" style="width: 200px;"></el-input>
          </el-form-item>
          <el-form-item label="储值时间" prop="date_range">
            <el-date-picker
              v-model="range"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="YYYY-MM-DD"
              @change="getTableDataList(1)"
            />
          </el-form-item>
          <el-form-item>
            <LbButton size="default" type="primary" style="margin-right: 5px" @click="getTableDataList(1)">
              搜索
            </LbButton>
            <LbButton size="default" style="margin-right: 5px" @click="resetForm">
              重置
            </LbButton>
            <LbButton size="default" type="success" @click="exportData">
              导出数据
            </LbButton>
          </el-form-item>
        </el-form>
      </el-row>
      
      <!-- 数据表格 -->
      <el-table 
        v-loading="loading" 
        :data="tableData" 
        :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
        style="width: 100%"
      >
        <el-table-column prop="id" label="ID" width="80" fixed />
        <el-table-column prop="avatarUrl" label="头像" width="80">
          <template #default="scope">
            <img :src="scope.row.avatarUrl" alt="头像" style="width: 40px; height: 40px; border-radius: 50%;" />
          </template>
        </el-table-column>
        <el-table-column prop="nickName" label="用户昵称" width="150" />
        <el-table-column prop="mobile" label="手机号" width="130" />
        <el-table-column prop="total_recharge" label="累计储值" width="120">
          <template #default="scope">
            <span style="color: #67c23a; font-weight: 600;">¥{{ scope.row.total_recharge || 0 }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="total_consume" label="累计消费" width="120">
          <template #default="scope">
            <span style="color: #e6a23c; font-weight: 600;">¥{{ scope.row.total_consume || 0 }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="current_balance" label="当前余额" width="120">
          <template #default="scope">
            <span style="color: #409eff; font-weight: 600;">¥{{ scope.row.current_balance || 0 }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="recharge_count" label="储值次数" width="100" />
        <el-table-column prop="last_recharge_time" label="最后储值" width="170">
          <template #default="scope">
            <div v-if="scope.row.last_recharge_time">
              <div>{{ formatDate(scope.row.last_recharge_time, 1) }}</div>
              <div>{{ formatDate(scope.row.last_recharge_time, 2) }}</div>
            </div>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column prop="create_time" label="注册时间" width="170">
          <template #default="scope">
            <div>{{ formatDate(scope.row.create_time, 1) }}</div>
            <div>{{ formatDate(scope.row.create_time, 2) }}</div>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <div class="table-operate">
              <LbButton
                size="mini"
                type="primary"
                @click="viewDetail(scope.row)"
              >
                查看详情
              </LbButton>
              <LbButton
                size="mini"
                type="success"
                @click="viewRecords(scope.row)"
              >
                储值记录
              </LbButton>
              <LbButton
                size="mini"
                type="warning"
                @click="adjustBalance(scope.row)"
              >
                余额调整
              </LbButton>
            </div>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination-section">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
    
    <!-- 用户详情对话框 -->
    <el-dialog v-model="detailVisible" title="用户储值详情" width="60%">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="用户ID">{{ userDetail.id }}</el-descriptions-item>
        <el-descriptions-item label="用户昵称">{{ userDetail.nickName }}</el-descriptions-item>
        <el-descriptions-item label="手机号">{{ userDetail.mobile }}</el-descriptions-item>
        <el-descriptions-item label="注册时间">{{ userDetail.create_time }}</el-descriptions-item>
        <el-descriptions-item label="累计储值">¥{{ userDetail.total_recharge || 0 }}</el-descriptions-item>
        <el-descriptions-item label="累计消费">¥{{ userDetail.total_consume || 0 }}</el-descriptions-item>
        <el-descriptions-item label="当前余额">¥{{ userDetail.current_balance || 0 }}</el-descriptions-item>
        <el-descriptions-item label="储值次数">{{ userDetail.recharge_count || 0 }}</el-descriptions-item>
      </el-descriptions>
      
      <template #footer>
        <LbButton @click="detailVisible = false">关闭</LbButton>
      </template>
    </el-dialog>
    
    <!-- 余额调整对话框 -->
    <el-dialog v-model="adjustVisible" title="余额调整" width="40%">
      <el-form :model="adjustForm" :rules="adjustRules" ref="adjustFormRef">
        <el-form-item label="用户昵称">
          <span>{{ adjustForm.nickName }}</span>
        </el-form-item>
        <el-form-item label="当前余额">
          <span>¥{{ adjustForm.current_balance }}</span>
        </el-form-item>
        <el-form-item label="调整类型" prop="type">
          <el-radio-group v-model="adjustForm.type">
            <el-radio :value="1">增加余额</el-radio>
            <el-radio :value="2">减少余额</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="调整金额" prop="amount">
          <el-input-number 
            v-model="adjustForm.amount" 
            :min="0.01" 
            :precision="2"
            style="width: 100%;"
          />
        </el-form-item>
        <el-form-item label="调整原因" prop="reason">
          <el-input 
            v-model="adjustForm.reason" 
            type="textarea" 
            :rows="3"
            placeholder="请输入调整原因"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <LbButton @click="adjustVisible = false">取消</LbButton>
        <LbButton type="primary" @click="submitAdjust" :loading="adjustLoading">确定调整</LbButton>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import TopNav from '@/components/common/TopNav.vue'
import LbButton from '@/components/common/LbButton.vue'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const tableData = ref([])
const searchFormRef = ref()
const range = ref([])
const detailVisible = ref(false)
const adjustVisible = ref(false)
const adjustFormRef = ref()
const adjustLoading = ref(false)

// 统计数据
const stats = reactive({
  total_users: 0,
  total_amount: 0,
  used_amount: 0,
  balance_amount: 0
})

// 搜索表单
const searchForm = reactive({
  nickName: '',
  mobile: '',
  start_time: '',
  end_time: ''
})

// 分页数据
const pagination = reactive({
  page: 1,
  pageSize: 10,
  total: 0
})

// 用户详情
const userDetail = reactive({
  id: '',
  nickName: '',
  mobile: '',
  create_time: '',
  total_recharge: 0,
  total_consume: 0,
  current_balance: 0,
  recharge_count: 0
})

// 余额调整表单
const adjustForm = reactive({
  id: '',
  nickName: '',
  current_balance: 0,
  type: 1,
  amount: 0,
  reason: ''
})

// 余额调整验证规则
const adjustRules = {
  type: [
    { required: true, message: '请选择调整类型', trigger: 'change' }
  ],
  amount: [
    { required: true, message: '请输入调整金额', trigger: 'blur' }
  ],
  reason: [
    { required: true, message: '请输入调整原因', trigger: 'blur' }
  ]
}

// 方法
const getTableDataList = async (page = 1) => {
  loading.value = true
  pagination.page = page
  
  try {
    const params = new URLSearchParams({
      page: pagination.page,
      pageSize: pagination.pageSize,
      nickName: searchForm.nickName,
      mobile: searchForm.mobile,
      start_time: searchForm.start_time,
      end_time: searchForm.end_time
    })
    
    const response = await fetch(`/api/finance/stored/list?${params}`)
    const result = await response.json()

    if (result.code === 200) {
      tableData.value = result.data.list || []
      pagination.total = result.data.total || 0
      // 更新统计数据
      Object.assign(stats, result.data.stats || {})
    } else {
      ElMessage.error(result.message || '获取数据失败')
    }
  } catch (error) {
    console.error('获取储值列表失败:', error)
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

const resetForm = () => {
  searchForm.nickName = ''
  searchForm.mobile = ''
  searchForm.start_time = ''
  searchForm.end_time = ''
  range.value = []
  getTableDataList(1)
}

const formatDate = (timestamp, type) => {
  if (!timestamp) return ''
  const date = new Date(timestamp)
  if (type === 1) {
    return date.toLocaleDateString()
  } else {
    return date.toLocaleTimeString()
  }
}

const viewDetail = async (row) => {
  try {
    const response = await fetch(`/api/finance/stored/detail/${row.id}`)
    const result = await response.json()
    
    if (result.code === 200) {
      Object.assign(userDetail, result.data)
      detailVisible.value = true
    } else {
      ElMessage.error(result.message || '获取用户详情失败')
    }
  } catch (error) {
    console.error('获取用户详情失败:', error)
    ElMessage.error('获取用户详情失败')
  }
}

const viewRecords = (row) => {
  router.push(`/finance/stored/records?user_id=${row.id}`)
}

const adjustBalance = (row) => {
  Object.assign(adjustForm, {
    id: row.id,
    nickName: row.nickName,
    current_balance: row.current_balance,
    type: 1,
    amount: 0,
    reason: ''
  })
  adjustVisible.value = true
}

const submitAdjust = async () => {
  try {
    await adjustFormRef.value.validate()
    
    adjustLoading.value = true
    
    const response = await fetch(`/api/finance/stored/adjust/${adjustForm.id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        type: adjustForm.type,
        amount: adjustForm.amount,
        reason: adjustForm.reason
      })
    })
    
    const result = await response.json()
    
    if (result.code === 200) {
      ElMessage.success('余额调整成功')
      adjustVisible.value = false
      getTableDataList()
    } else {
      ElMessage.error(result.message || '调整失败')
    }
  } catch (error) {
    console.error('余额调整失败:', error)
    ElMessage.error('调整失败')
  } finally {
    adjustLoading.value = false
  }
}

const exportData = async () => {
  try {
    const params = new URLSearchParams({
      nickName: searchForm.nickName,
      mobile: searchForm.mobile,
      start_time: searchForm.start_time,
      end_time: searchForm.end_time
    })
    
    const response = await fetch(`/api/finance/stored/export?${params}`)
    const result = await response.json()
    
    if (result.code === 200) {
      ElMessage.success('导出成功')
      // 这里可以添加下载文件的逻辑
    } else {
      ElMessage.error(result.message || '导出失败')
    }
  } catch (error) {
    console.error('导出数据失败:', error)
    ElMessage.error('导出失败')
  }
}

const handleSizeChange = (size) => {
  pagination.pageSize = size
  getTableDataList(1)
}

const handleCurrentChange = (page) => {
  getTableDataList(page)
}

// 监听日期范围变化
const watchRange = () => {
  if (range.value && range.value.length === 2) {
    searchForm.start_time = range.value[0]
    searchForm.end_time = range.value[1]
  } else {
    searchForm.start_time = ''
    searchForm.end_time = ''
  }
}

// 生命周期
onMounted(() => {
  getTableDataList()
})
</script>

<style scoped>
.lb-finance-stored {
  padding: 20px;
  background-color: #fff;
  min-height: calc(100vh - 60px);
}

.stats-cards {
  margin-bottom: 20px;
}

/* 统计内容布局 */
.stat-content {
  text-align: center;
  padding: 10px 0;
  position: relative;
  z-index: 2;
}

.stats-value {
  font-size: 28px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 8px;
}

.stats-label {
  font-size: 14px;
  color: #909399;
}

.stats-icon {
  position: absolute;
  top: 20px;
  right: 20px;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: #fff;
}

.stats-icon.users {
  background: linear-gradient(135deg, #409eff, #66b1ff);
}

.stats-icon.amount {
  background: linear-gradient(135deg, #67c23a, #85ce61);
}

.stats-icon.used {
  background: linear-gradient(135deg, #e6a23c, #f0a020);
}

.stats-icon.balance {
  background: linear-gradient(135deg, #f56c6c, #f78989);
}

.page-search-form {
  margin-bottom: 16px;
  padding: 16px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.table-operate {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.table-operate .el-button {
  margin: 0;
}

.pagination-section {
  margin-top: 20px;
  text-align: right;
}

@media (max-width: 768px) {
  .lb-finance-stored {
    padding: 10px;
  }

  .stats-cards .el-col {
    margin-bottom: 10px;
  }

  .table-operate {
    flex-direction: column;
  }

  .pagination-section {
    text-align: center;
  }
}
</style>
