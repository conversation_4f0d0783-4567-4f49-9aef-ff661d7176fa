<!--
  订单管理页面 - ShopOrder.vue
  基于接口文档实现完整的订单管理功能
  包含：订单列表、搜索筛选、导出、详情查看、排行榜等功能
-->

<template>
  <div class="page-container">
    <!-- 顶部导航 -->
    <TopNav title="订单管理" />

    <div class="content-container">
      <!-- 统计卡片 -->
      <el-row :gutter="20" class="stats-cards">
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-value">{{ total || 0 }}</div>
              <div class="stats-label">订单总数</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-value">¥{{ totalAmount || '0.00' }}</div>
              <div class="stats-label">订单总金额</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-value">{{ todayOrderCount || 0 }}</div>
              <div class="stats-label">今日订单</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-value">¥{{ todayAmount || '0.00' }}</div>
              <div class="stats-label">今日金额</div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 搜索表单 -->
      <div class="search-form-container">
        <el-form
          ref="searchFormRef"
          :model="searchForm"
          :inline="true"
          class="search-form"
        >
          <el-row :gutter="20">
            <el-col :span="24">
              <!-- 第一行搜索条件 -->
              <el-form-item label="订单号" prop="orderCode">
                <el-input
                  size="default"
                  v-model="searchForm.orderCode"
                  placeholder="请输入订单号"
                  clearable
                  style="width: 200px"
                />
              </el-form-item>

              <el-form-item label="商品名称" prop="goodsName">
                <el-input
                  size="default"
                  v-model="searchForm.goodsName"
                  placeholder="请输入商品名称"
                  clearable
                  style="width: 200px"
                />
              </el-form-item>

              <el-form-item label="师傅姓名" prop="coachName">
                <el-input
                  size="default"
                  v-model="searchForm.coachName"
                  placeholder="请输入师傅姓名"
                  clearable
                  style="width: 200px"
                />
              </el-form-item>

              <el-form-item label="订单状态" prop="payType">
                <el-select
                  size="default"
                  v-model="searchForm.payType"
                  placeholder="请选择订单状态"
                  clearable
                  style="width: 180px"
                >
                  <el-option label="待报价" :value="-3" />
                  <el-option label="已报价(未选择报价)" :value="-2" />
                  <el-option label="取消订单" :value="-1" />
                  <el-option label="待支付" :value="1" />
                  <el-option label="已支付，师傅未接单" :value="2" />
                  <el-option label="师傅已接单待上门" :value="3" />
                  <el-option label="待预约" :value="4" />
                  <el-option label="待服务（已上门）" :value="5" />
                  <el-option label="服务中（开始服务）" :value="6" />
                  <el-option label="已完成" :value="7" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="24">
              <!-- 第二行搜索条件 -->
              <el-form-item label="订单类型" prop="type">
                <el-select
                  size="default"
                  v-model="searchForm.type"
                  placeholder="请选择订单类型"
                  clearable
                  style="width: 150px"
                >
                  <el-option label="一口价模式" :value="0" />
                  <el-option label="报价模式" :value="1" />
                </el-select>
              </el-form-item>

              <el-form-item label="地址" prop="address">
                <el-input
                  size="default"
                  v-model="searchForm.address"
                  placeholder="请输入地址"
                  clearable
                  style="width: 200px"
                />
              </el-form-item>

              <el-form-item label="时间范围" prop="timeRange">
                <el-date-picker
                  size="default"
                  v-model="timeRange"
                  type="datetimerange"
                  range-separator="至"
                  start-placeholder="开始时间"
                  end-placeholder="结束时间"
                  format="YYYY-MM-DD HH:mm:ss"
                  value-format="YYYY-MM-DD HH:mm:ss"
                  style="width: 350px"
                  @change="handleTimeRangeChange"
                />
              </el-form-item>

              <!-- 操作按钮 -->
              <el-form-item>
                <LbButton
                  size="default"
                  type="primary"
                  icon="Search"
                  @click="handleSearch"
                  :loading="loading"
                >
                  搜索
                </LbButton>
                <LbButton
                  size="default"
                  icon="RefreshLeft"
                  @click="handleReset"
                >
                  重置
                </LbButton>
                <LbButton
                  size="default"
                  type="success"
                  icon="Download"
                  @click="handleExport"
                  :loading="exportLoading"
                >
                  导出
                </LbButton>
                <LbButton
                  size="default"
                  type="warning"
                  icon="TrendCharts"
                  @click="showRankDialog = true"
                >
                  排行榜
                </LbButton>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>

      <!-- 表格容器 -->
      <div class="table-container">
        <el-table
          v-loading="loading"
          :data="tableData"
          :header-cell-style="{
            background: '#f5f7fa',
            color: '#606266',
            fontSize: '16px',
            fontWeight: '600'
          }"
          :cell-style="{
            fontSize: '14px',
            padding: '12px 8px'
          }"
          style="width: 100%"
        >
          <el-table-column prop="id" label="ID" width="80" align="center" />
          <el-table-column prop="orderCode" label="订单号" min-width="200" />
          <el-table-column prop="goodsName" label="商品名称" min-width="150" />
          <el-table-column prop="num" label="数量" width="80" align="center" />
          <el-table-column prop="payPrice" label="支付金额" width="100" align="center">
            <template #default="{ row }">
              <span class="price-text">¥{{ row.payPrice }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="nickName" label="用户昵称" width="120" />
          <el-table-column prop="phone" label="用户手机" width="120" />
          <el-table-column prop="coachName" label="师傅姓名" width="120" />
          <el-table-column prop="coachMobile" label="师傅手机" width="120" />
          <el-table-column prop="payType" label="订单状态" width="150" align="center">
            <template #default="{ row }">
              <el-tag :type="getPayTypeTagType(row.payType)">
                {{ getPayTypeText(row.payType) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="type" label="订单类型" width="100" align="center">
            <template #default="{ row }">
              <el-tag :type="row.type === 0 ? 'warning' : 'primary'">
                {{ row.type === 0 ? '一口价模式' : '报价模式' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="createTime" label="创建时间" width="160" />
          <el-table-column label="操作" width="120" align="center" fixed="right">
            <template #default="scope">
              <div class="table-operate">
                <LbButton
                  size="default"
                  type="primary"
                  @click="handleViewDetail(scope.row)"
                >
                  详情
                </LbButton>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页组件 -->
      <LbPage
        :page="searchForm.pageNum"
        :page-size="searchForm.pageSize"
        :total="total"
        @handleSizeChange="handleSizeChange"
        @handleCurrentChange="handleCurrentChange"
      />
    </div>

    <!-- 订单详情弹窗 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="订单详情"
      width="900px"
      :close-on-click-modal="false"
    >
      <div v-if="orderDetail" class="detail-content">
        <!-- 用户信息 -->
        <div class="detail-section">
          <h4>用户信息</h4>
          <div class="detail-item">
            <label>用户ID：</label>
            <span>{{ orderDetail.userId || '-' }}</span>
          </div>
          <div class="detail-item">
            <label>下单人：</label>
            <span>{{ orderDetail.addressInfo?.userName || '-' }}</span>
          </div>
          <div class="detail-item">
            <label>联系方式：</label>
            <span>{{ orderDetail.addressInfo?.mobile || '-' }}</span>
          </div>
          <div class="detail-item">
            <label>项目地址：</label>
            <span>{{ orderDetail.addressInfo?.addressInfo || '-' }}</span>
          </div>
        </div>

        <!-- 师傅信息 -->
        <div class="detail-section" v-if="orderDetail.coachInfo">
          <h4>师傅信息</h4>
          <div class="detail-item">
            <label>师傅ID：</label>
            <span>{{ orderDetail.coachInfo.id || '-' }}</span>
          </div>
          <div class="detail-item">
            <label>师傅姓名：</label>
            <span>{{ orderDetail.coachInfo.coachName || '-' }}</span>
          </div>
          <div class="detail-item">
            <label>联系方式：</label>
            <span>{{ orderDetail.coachInfo.mobileEncry || '-' }}</span>
          </div>
          <div class="detail-item">
            <label>师傅等级：</label>
            <span>{{ orderDetail.coachInfo.labelName || '-' }}</span>
          </div>
          <div class="detail-item">
            <label>师傅地址：</label>
            <span>{{ orderDetail.coachInfo.address || '-' }}</span>
          </div>
        </div>

        <!-- 订单信息 -->
        <div class="detail-section">
          <h4>订单信息</h4>
          <div class="detail-item">
            <label>系统订单号：</label>
            <span>{{ orderDetail.orderCode || '-' }}</span>
          </div>
          <div class="detail-item">
            <label>下单时间：</label>
            <span>{{ orderDetail.createTime || '-' }}</span>
          </div>
          <div class="detail-item">
            <label>项目时间：</label>
            <span>{{ formatServiceTime(orderDetail.startTime, orderDetail.endTime) }}</span>
          </div>
          <div class="detail-item">
            <label>项目费用：</label>
            <span class="price-text">¥{{ orderDetail.servicePrice || '0.00' }}</span>
          </div>
          <div class="detail-item">
            <label>订单金额：</label>
            <span class="price-text">¥{{ orderDetail.payPrice || '0.00' }}</span>
          </div>
          <div class="detail-item">
            <label>支付方式：</label>
            <span>{{ getPayMethodText(orderDetail.payType) }}</span>
          </div>
          <div class="detail-item">
            <label>订单状态：</label>
            <el-tag :type="getPayTypeTagType(orderDetail.payType)">
              {{ getPayTypeText(orderDetail.payType) }}
            </el-tag>
          </div>
        </div>

        <!-- 项目内容 -->
        <div class="detail-section" v-if="orderDetail.orderGoods && orderDetail.orderGoods.length > 0">
          <h4>项目内容</h4>
          <div v-for="(goods, index) in orderDetail.orderGoods" :key="index" class="goods-item">
            <div class="goods-info">
              <div class="goods-image">
                <el-image
                  :src="goods.goodsCover"
                  style="width: 60px; height: 60px; border-radius: 4px;"
                  fit="cover"
                  :preview-src-list="[goods.goodsCover]"
                />
              </div>
              <div class="goods-details">
                <div class="goods-name">{{ goods.goodsName }}</div>
                <div class="goods-quantity">x{{ goods.num }}</div>
                <div class="goods-price">¥{{ goods.price }}</div>
              </div>
            </div>
            <!-- 项目配置信息 -->
            <div v-if="goods.priceSetting && goods.priceSetting.length > 0" class="goods-settings">
              <div v-for="(setting, settingIndex) in goods.priceSetting" :key="settingIndex" class="setting-item">
                <span class="setting-label">{{ setting.problemDesc }}：</span>
                <span class="setting-value">{{ setting.val }}</span>
              </div>
            </div>
          </div>
          <div class="goods-total">
            <span>合计数量：{{ getTotalQuantity() }}</span>
          </div>
        </div>

        <!-- 其他信息 -->
        <div class="detail-section">
          <h4>其他信息</h4>
          <div class="detail-item">
            <label>微信交易号：</label>
            <span>{{ orderDetail.transactionId || '-' }}</span>
          </div>
          <div class="detail-item">
            <label>支付时间：</label>
            <span>{{ orderDetail.payTime || '-' }}</span>
          </div>
          <div class="detail-item">
            <label>订单类型：</label>
            <el-tag :type="orderDetail.type === 0 ? 'warning' : 'primary'">
              {{ orderDetail.type === 0 ? '一口价模式' : '报价模式' }}
            </el-tag>
          </div>
          <div class="detail-item">
            <label>退款状态：</label>
            <el-tag :type="orderDetail.refundStatus === 0 ? 'success' : 'warning'">
              {{ orderDetail.refundStatus === 0 ? '未退款' : '已退款' }}
            </el-tag>
          </div>
        </div>
      </div>
      <template #footer>
        <LbButton @click="detailDialogVisible = false">关闭</LbButton>
      </template>
    </el-dialog>

    <!-- 排行榜弹窗 -->
    <el-dialog
      v-model="showRankDialog"
      title="排行榜统计"
      width="1000px"
      :close-on-click-modal="false"
    >
      <div class="rank-container">
        <!-- 排行榜筛选条件 -->
        <el-form :model="rankForm" :inline="true" class="rank-form">
          <el-form-item label="排行数量">
            <el-select v-model="rankForm.top" placeholder="选择排行数量" style="width: 120px">
              <el-option label="前5名" :value="5" />
              <el-option label="前10名" :value="10" />
              <el-option label="前20名" :value="20" />
            </el-select>
          </el-form-item>
          <el-form-item label="时间范围">
            <el-date-picker
              v-model="rankTimeRange"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              style="width: 350px"
              @change="handleRankTimeRangeChange"
            />
          </el-form-item>
          <el-form-item>
            <LbButton type="primary" @click="loadRankData" :loading="rankLoading">
              查询排行榜
            </LbButton>
          </el-form-item>
        </el-form>

        <!-- 排行榜标签页 -->
        <el-tabs v-model="activeRankTab" @tab-click="handleRankTabClick">
          <!-- 师傅收入排行榜 -->
          <el-tab-pane label="师傅收入排行榜" name="coachIncome">
            <el-table
              v-loading="rankLoading"
              :data="coachIncomeRank"
              style="width: 100%"
              :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
            >
              <el-table-column type="index" label="排名" width="80" align="center">
                <template #default="{ $index }">
                  <el-tag
                    :type="$index < 3 ? 'danger' : 'info'"
                    effect="dark"
                  >
                    {{ $index + 1 }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="coachId" label="师傅ID" width="100" align="center" />
              <el-table-column prop="coachName" label="师傅姓名" width="120">
                <template #default="{ row }">
                  {{ row.coachName || '-' }}
                </template>
              </el-table-column>
              <el-table-column prop="coachMobile" label="师傅手机" width="130">
                <template #default="{ row }">
                  {{ row.coachMobile || '-' }}
                </template>
              </el-table-column>
              <el-table-column prop="totalIncome" label="总收入" width="120" align="center">
                <template #default="{ row }">
                  <span class="income-text">¥{{ row.totalIncome }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="orderCount" label="订单数量" width="100" align="center" />
            </el-table>
          </el-tab-pane>

          <!-- 师傅跑单排行榜 -->
          <el-tab-pane label="师傅跑单排行榜" name="coachCancel">
            <el-table
              v-loading="rankLoading"
              :data="coachCancelRank"
              style="width: 100%"
              :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
            >
              <el-table-column type="index" label="排名" width="80" align="center">
                <template #default="{ $index }">
                  <el-tag
                    :type="$index < 3 ? 'danger' : 'info'"
                    effect="dark"
                  >
                    {{ $index + 1 }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="coachId" label="师傅ID" width="100" align="center" />
              <el-table-column prop="coachName" label="师傅姓名" width="120">
                <template #default="{ row }">
                  {{ row.coachName || '-' }}
                </template>
              </el-table-column>
              <el-table-column prop="coachMobile" label="师傅手机" width="130">
                <template #default="{ row }">
                  {{ row.coachMobile || '-' }}
                </template>
              </el-table-column>
              <el-table-column prop="cancelCount" label="跑单次数" width="100" align="center" />
              <el-table-column prop="cancelRate" label="跑单率" width="100" align="center">
                <template #default="{ row }">
                  <el-tag :type="row.cancelRate > 30 ? 'danger' : row.cancelRate > 15 ? 'warning' : 'success'">
                    {{ row.cancelRate }}%
                  </el-tag>
                </template>
              </el-table-column>
            </el-table>
          </el-tab-pane>

          <!-- 用户跑单排行榜 -->
          <el-tab-pane label="用户跑单排行榜" name="userCancel">
            <el-table
              v-loading="rankLoading"
              :data="userCancelRank"
              style="width: 100%"
              :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
            >
              <el-table-column type="index" label="排名" width="80" align="center">
                <template #default="{ $index }">
                  <el-tag
                    :type="$index < 3 ? 'danger' : 'info'"
                    effect="dark"
                  >
                    {{ $index + 1 }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="userId" label="用户ID" width="100" align="center" />
              <el-table-column prop="nickName" label="用户昵称" width="120">
                <template #default="{ row }">
                  {{ row.nickName || '-' }}
                </template>
              </el-table-column>
              <el-table-column prop="phone" label="用户手机" width="130">
                <template #default="{ row }">
                  {{ row.phone || '-' }}
                </template>
              </el-table-column>
              <el-table-column prop="cancelCount" label="跑单次数" width="100" align="center" />
              <el-table-column prop="cancelRate" label="跑单率" width="100" align="center">
                <template #default="{ row }">
                  <el-tag :type="row.cancelRate > 30 ? 'danger' : row.cancelRate > 15 ? 'warning' : 'success'">
                    {{ row.cancelRate }}%
                  </el-tag>
                </template>
              </el-table-column>
            </el-table>
          </el-tab-pane>
        </el-tabs>
      </div>
      <template #footer>
        <LbButton @click="showRankDialog = false">关闭</LbButton>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, getCurrentInstance } from 'vue'
import { ElMessage } from 'element-plus'
import TopNav from '@/components/common/TopNav.vue'
import LbButton from '@/components/common/LbButton.vue'
import LbPage from '@/components/common/LbPage.vue'

const { proxy } = getCurrentInstance()

// 响应式数据
const loading = ref(false)
const exportLoading = ref(false)
const rankLoading = ref(false)
const tableData = ref([])
const total = ref(0)
const timeRange = ref([])
const rankTimeRange = ref([])

// 统计数据
const totalAmount = ref(0)
const todayOrderCount = ref(0)
const todayAmount = ref(0)

// 弹窗控制
const detailDialogVisible = ref(false)
const showRankDialog = ref(false)
const orderDetail = ref(null)

// 排行榜相关
const activeRankTab = ref('coachIncome')
const coachIncomeRank = ref([])
const coachCancelRank = ref([])
const userCancelRank = ref([])

// 搜索表单
const searchForm = reactive({
  pageNum: 1,
  pageSize: 10,
  orderCode: '',
  goodsName: '',
  coachName: '',
  payType: null,
  type: null,
  startTime: '',
  endTime: '',
  address: ''
})

// 排行榜表单
const rankForm = reactive({
  top: 10,
  startTime: '',
  endTime: ''
})

// 搜索表单引用
const searchFormRef = ref(null)

/**
 * 获取支付方式文本
 */
const getPayTypeText = (payType) => {
  const payTypeMap = {
    '-3': '待报价',
    '-2': '已报价(未选择报价)',
    '-1': '取消订单',
    '1': '待支付',
    '2': '已支付，师傅未接单',
    '3': '师傅已接单待上门',
    '4': '待预约',
    '5': '待服务（已上门）',
    '6': '服务中（开始服务）',
    '7': '已完成'
  }
  return payTypeMap[payType] || '未知'
}

/**
 * 获取支付方式标签类型
 */
const getPayTypeTagType = (payType) => {
  const typeMap = {
    '-3': 'info',      // 待报价
    '-2': 'warning',   // 已报价(未选择报价)
    '-1': 'danger',    // 取消订单
    '1': 'warning',    // 待支付
    '2': 'primary',    // 已支付，师傅未接单
    '3': 'primary',    // 师傅已接单待上门
    '4': 'warning',    // 待预约
    '5': 'success',    // 待服务（已上门）
    '6': 'success',    // 服务中（开始服务）
    '7': 'success'     // 已完成
  }
  return typeMap[payType] || 'info'
}

/**
 * 获取支付方式文本（用于显示具体的支付方式）
 */
const getPayMethodText = (payType) => {
  // 根据实际的支付类型返回支付方式
  if (payType === 1 || payType === '1') {
    return '微信支付'
  } else if (payType === 2 || payType === '2') {
    return '支付宝'
  } else if (payType === 7 || payType === '7') {
    return '余额支付'
  }
  return '微信支付' // 默认显示微信支付
}

/**
 * 格式化服务时间
 */
const formatServiceTime = (startTime, endTime) => {
  if (!startTime || !endTime) return '-'

  const start = new Date(startTime)
  const end = new Date(endTime)

  const formatTime = (date) => {
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    const hours = String(date.getHours()).padStart(2, '0')
    const minutes = String(date.getMinutes()).padStart(2, '0')

    return `${year}-${month}-${day} ${hours}:${minutes}`
  }

  return `${formatTime(start)} - ${formatTime(end)}`
}

/**
 * 计算商品总数量
 */
const getTotalQuantity = () => {
  if (!orderDetail.value || !orderDetail.value.orderGoods) return 0

  return orderDetail.value.orderGoods.reduce((total, goods) => {
    return total + (goods.num || 0)
  }, 0)
}

/**
 * 处理时间范围变化
 */
const handleTimeRangeChange = (value) => {
  if (value && value.length === 2) {
    searchForm.startTime = value[0]
    searchForm.endTime = value[1]
  } else {
    searchForm.startTime = ''
    searchForm.endTime = ''
  }
}

/**
 * 处理排行榜时间范围变化
 */
const handleRankTimeRangeChange = (value) => {
  if (value && value.length === 2) {
    rankForm.startTime = value[0]
    rankForm.endTime = value[1]
  } else {
    rankForm.startTime = ''
    rankForm.endTime = ''
  }
}

/**
 * 计算统计数据
 */
const calculateStats = (orderList) => {
  // 计算总金额
  totalAmount.value = orderList.reduce((sum, order) => {
    return sum + (parseFloat(order.payPrice) || 0)
  }, 0).toFixed(2)

  // 计算今日订单数和金额
  const today = new Date().toISOString().split('T')[0] // 获取今日日期 YYYY-MM-DD
  const todayOrders = orderList.filter(order => {
    return order.createTime && order.createTime.startsWith(today)
  })

  todayOrderCount.value = todayOrders.length
  todayAmount.value = todayOrders.reduce((sum, order) => {
    return sum + (parseFloat(order.payPrice) || 0)
  }, 0).toFixed(2)
}

/**
 * 加载订单列表
 */
const loadOrderList = async () => {
  try {
    loading.value = true
    console.log('🔍 开始加载订单列表，参数:', searchForm)

    // 构建请求参数，只传递有值的参数
    const requestParams = {
      pageNum: searchForm.pageNum,
      pageSize: searchForm.pageSize
    }

    // 只有在搜索时才添加其他参数
    if (searchForm.orderCode) requestParams.orderCode = searchForm.orderCode
    if (searchForm.goodsName) requestParams.goodsName = searchForm.goodsName
    if (searchForm.coachName) requestParams.coachName = searchForm.coachName
    if (searchForm.payType !== null) requestParams.payType = searchForm.payType
    if (searchForm.type !== null) requestParams.type = searchForm.type
    if (searchForm.startTime) requestParams.startTime = searchForm.startTime
    if (searchForm.endTime) requestParams.endTime = searchForm.endTime
    if (searchForm.address) requestParams.address = searchForm.address

    const response = await proxy.$api.shop.orderList(requestParams)
    console.log('📋 订单列表响应:', response)

    if (response.code === '200') {
      tableData.value = response.data.list || []
      total.value = response.data.totalCount || 0

      // 计算统计数据
      calculateStats(response.data.list || [])

      console.log(`✅ 订单列表加载成功，共 ${total.value} 条数据`)
    } else {
      ElMessage.error(response.msg || '获取订单列表失败')
    }
  } catch (error) {
    console.error('❌ 加载订单列表失败:', error)
    ElMessage.error('获取订单列表失败')
  } finally {
    loading.value = false
  }
}

/**
 * 搜索处理
 */
const handleSearch = () => {
  searchForm.pageNum = 1
  loadOrderList()
}

/**
 * 重置搜索
 */
const handleReset = () => {
  searchFormRef.value?.resetFields()
  Object.assign(searchForm, {
    pageNum: 1,
    pageSize: 10,
    orderCode: '',
    goodsName: '',
    coachName: '',
    payType: null,
    type: null,
    startTime: '',
    endTime: '',
    address: ''
  })
  timeRange.value = []
  loadOrderList()
}

/**
 * 分页大小变化
 */
const handleSizeChange = (size) => {
  searchForm.pageSize = size
  searchForm.pageNum = 1
  loadOrderList()
}

/**
 * 当前页变化
 */
const handleCurrentChange = (page) => {
  searchForm.pageNum = page
  loadOrderList()
}

/**
 * 导出订单列表 - POST方法，传递JSON body参数
 */
const handleExport = async () => {
  try {
    exportLoading.value = true
    console.log('📤 开始导出订单Excel...')

    // 构建导出参数JSON对象
    const exportParams = {}

    // 添加订单编号
    if (searchForm.orderCode !== '' && searchForm.orderCode !== null && searchForm.orderCode !== undefined) {
      exportParams.orderCode = searchForm.orderCode
    }

    // 添加商品名称
    if (searchForm.goodsName !== '' && searchForm.goodsName !== null && searchForm.goodsName !== undefined) {
      exportParams.goodsName = searchForm.goodsName
    }

    // 添加师傅名称
    if (searchForm.coachName !== '' && searchForm.coachName !== null && searchForm.coachName !== undefined) {
      exportParams.coachName = searchForm.coachName
    }

    // 添加支付类型
    if (searchForm.payType !== '' && searchForm.payType !== null && searchForm.payType !== undefined) {
      exportParams.payType = parseInt(searchForm.payType)
    }

    // 添加订单类型
    if (searchForm.type !== '' && searchForm.type !== null && searchForm.type !== undefined) {
      exportParams.type = parseInt(searchForm.type)
    }

    // 添加地址
    if (searchForm.address !== '' && searchForm.address !== null && searchForm.address !== undefined) {
      exportParams.address = searchForm.address
    }

    // 添加时间范围
    if (searchForm.startTime !== '' && searchForm.startTime !== null && searchForm.startTime !== undefined) {
      exportParams.startTime = searchForm.startTime
    }

    if (searchForm.endTime !== '' && searchForm.endTime !== null && searchForm.endTime !== undefined) {
      exportParams.endTime = searchForm.endTime
    }

    // 添加分页参数（根据接口要求）
    exportParams.pageNum = 1
    exportParams.pageSize = 10

    console.log('📤 导出参数:', exportParams)

    // 使用fetch发送POST请求下载文件
    const token = sessionStorage.getItem('minitk')
    const response = await fetch('/api/admin/order/export', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...(token && { 'Authorization': `Bearer ${token}` })
      },
      body: JSON.stringify(exportParams)
    })

    if (response.ok) {
      // 检查响应内容类型
      const contentType = response.headers.get('Content-Type')

      // 如果是JSON响应，说明可能是错误信息
      if (contentType && contentType.includes('application/json')) {
        const errorData = await response.json()
        console.error('❌ 导出返回错误:', errorData)

        if (errorData.code === '-1' || errorData.code === -1) {
          // 显示具体的错误信息
          const errorMsg = errorData.msg || '导出失败'
          ElMessage.error(`导出失败: ${errorMsg}`)

          // 如果是数据库字段映射错误，给出更友好的提示
          if (errorMsg.includes('coachMobile') || errorMsg.includes('ResultMapException')) {
            ElMessage.warning('后端数据库字段映射异常，请联系技术人员修复')
          }
        } else {
          ElMessage.error(errorData.msg || '导出失败')
        }
        return
      }

      // 获取文件名（从响应头或使用默认名称）
      const contentDisposition = response.headers.get('Content-Disposition')
      let filename = `订单导出_${new Date().toISOString().slice(0, 10)}.xlsx`

      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/)
        if (filenameMatch && filenameMatch[1]) {
          filename = filenameMatch[1].replace(/['"]/g, '')
        }
      }

      // 创建blob并下载
      const blob = await response.blob()
      const url = window.URL.createObjectURL(blob)

      const link = document.createElement('a')
      link.href = url
      link.download = filename
      link.style.display = 'none'

      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      // 清理URL对象
      window.URL.revokeObjectURL(url)

      ElMessage.success('导出成功，请查看浏览器下载')
      console.log('✅ 导出订单Excel成功')
    } else {
      // 尝试解析错误响应
      try {
        const errorText = await response.text()
        console.error('❌ 导出HTTP错误:', response.status, response.statusText, errorText)

        // 尝试解析JSON错误信息
        try {
          const errorData = JSON.parse(errorText)
          if (errorData.msg) {
            ElMessage.error(`导出失败: ${errorData.msg}`)
          } else {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`)
          }
        } catch (parseError) {
          throw new Error(`导出失败: HTTP ${response.status} ${response.statusText}`)
        }
      } catch (textError) {
        throw new Error(`导出失败: HTTP ${response.status} ${response.statusText}`)
      }
    }

  } catch (error) {
    console.error('❌ 导出订单Excel异常:', error)
    ElMessage.error('导出失败，请稍后重试')
  } finally {
    exportLoading.value = false
  }
}

/**
 * 查看订单详情
 */
const handleViewDetail = async (row) => {
  try {
    console.log('📄 查看订单详情:', row.id)

    const response = await proxy.$api.shop.orderDetail(row.id)
    console.log('📄 订单详情响应:', response)

    if (response.code === '200') {
      orderDetail.value = response.data
      detailDialogVisible.value = true
    } else {
      ElMessage.error(response.msg || '获取订单详情失败')
    }
  } catch (error) {
    console.error('❌ 获取订单详情失败:', error)
    ElMessage.error('获取订单详情失败')
  }
}

/**
 * 加载排行榜数据
 */
const loadRankData = async () => {
  try {
    rankLoading.value = true
    console.log('📊 加载排行榜数据，当前标签:', activeRankTab.value)

    const params = {
      top: rankForm.top,
      startTime: rankForm.startTime,
      endTime: rankForm.endTime
    }

    if (activeRankTab.value === 'coachIncome') {
      const response = await proxy.$api.shop.coachIncomeRank(params)
      if (response.code === '200') {
        coachIncomeRank.value = response.data || []
        console.log('💰 师傅收入排行榜加载成功:', coachIncomeRank.value.length)
      }
    } else if (activeRankTab.value === 'coachCancel') {
      const response = await proxy.$api.shop.coachCancelRank(params)
      if (response.code === '200') {
        coachCancelRank.value = response.data || []
        console.log('🏃 师傅跑单排行榜加载成功:', coachCancelRank.value.length)
      }
    } else if (activeRankTab.value === 'userCancel') {
      const response = await proxy.$api.shop.userCancelRank(params)
      if (response.code === '200') {
        userCancelRank.value = response.data || []
        console.log('👤 用户跑单排行榜加载成功:', userCancelRank.value.length)
      }
    }
  } catch (error) {
    console.error('❌ 加载排行榜数据失败:', error)
    ElMessage.error('加载排行榜数据失败')
  } finally {
    rankLoading.value = false
  }
}

/**
 * 排行榜标签切换
 */
const handleRankTabClick = (tab) => {
  console.log('🔄 切换排行榜标签:', tab.props.name)
  activeRankTab.value = tab.props.name
  loadRankData()
}

// 组件挂载时加载数据
onMounted(() => {
  console.log('🚀 订单管理页面初始化')
  loadOrderList()
})
</script>

<style scoped>
/* ===== 核心样式实现 ===== */

/* 1. 页面容器 - 基础布局 */
.page-container {
  padding: 0px;
}

/* 2. 内容容器 - 白色背景 + 圆角 + 阴影 */
  .content-container {
  background: white;
  border-radius: 8px;
  padding: 20px;
  /* box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1); */
}

/* 2.1 统计卡片样式 */
.stats-cards {
  margin-bottom: 20px;
}

.stats-card {
  border-radius: 8px;
  border: 1px solid #e4e7ed;
  transition: all 0.3s ease;
}

.stats-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.stats-content {
  text-align: center;
  padding: 10px 0;
}

.stats-value {
  font-size: 28px;
  font-weight: 700;
  color: #409eff;
  margin-bottom: 8px;
  line-height: 1;
}

.stats-label {
  font-size: 14px;
  color: #606266;
  font-weight: 500;
}

/* 3. 搜索表单 - 灰色背景区域 */
.search-form {
  margin-bottom: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 6px;
}

.search-form .el-form-item {
  margin-bottom: 10px;
  margin-right: 20px;
}

.search-form .el-form-item__label {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

/* 4. 表格容器 - 关键的阴影和圆角效果 */
.table-container {
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

/* 5. 表格样式 - 深度选择器覆盖Element Plus默认样式 */
:deep(.el-table) {
  border-left: 1px solid #ebeef5;
  border-right: 1px solid #ebeef5;
}

/* 表头样式 */
.el-table .el-table__header-wrapper th {
  background-color: #f5f7fa !important;
  color: #606266 !important;
  font-size: 16px !important;
  font-weight: 600 !important;
  padding: 15px 8px !important;
}

/* 表格内容样式 */
.el-table .el-table__body-wrapper td {
  font-size: 14px !important;
  padding: 12px 8px !important;
  color: #333 !important;
}

/* 行悬停效果 */
.el-table .el-table__row:hover {
  background-color: #f8f9fa !important;
}

/* 6. 价格文本样式 */
.price-text {
  color: #e6a23c;
  font-weight: 600;
}

.income-text {
  color: #67c23a;
  font-weight: 600;
  font-size: 16px;
}

/* 7. 订单详情样式 */
.detail-content {
  padding: 10px 0;
}

.detail-section {
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #f0f0f0;
}

.detail-section:last-child {
  border-bottom: none;
}

.detail-section h4 {
  margin: 0 0 15px 0;
  color: #333;
  font-size: 16px;
  font-weight: 600;
}

.detail-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 10px;
  padding: 5px 0;
}

.detail-item:last-child {
  margin-bottom: 0;
}

.detail-item label {
  font-weight: 600;
  color: #333;
  width: 120px;
  flex-shrink: 0;
  margin-right: 15px;
}

.detail-item span {
  color: #666;
  flex: 1;
}

/* 商品项目样式 */
.goods-item {
  margin-bottom: 15px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.goods-info {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.goods-image {
  margin-right: 15px;
}

.goods-details {
  flex: 1;
}

.goods-name {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 5px;
}

.goods-quantity {
  font-size: 14px;
  color: #666;
  margin-bottom: 5px;
}

.goods-price {
  font-size: 16px;
  font-weight: 600;
  color: #e6a23c;
}

.goods-settings {
  margin-top: 10px;
  padding-top: 10px;
  border-top: 1px solid #e9ecef;
}

.setting-item {
  display: flex;
  margin-bottom: 5px;
  font-size: 14px;
}

.setting-label {
  font-weight: 500;
  color: #666;
  width: 120px;
  flex-shrink: 0;
}

.setting-value {
  color: #333;
  flex: 1;
}

.goods-total {
  margin-top: 15px;
  padding-top: 10px;
  border-top: 1px solid #e9ecef;
  text-align: right;
  font-weight: 600;
  color: #333;
}

/* 8. 排行榜容器样式 */
.rank-container {
  padding: 10px 0;
}

.rank-form {
  margin-bottom: 20px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 6px;
}

.rank-form .el-form-item {
  margin-bottom: 0;
  margin-right: 20px;
}

/* 9. 按钮样式优化 */
.el-button {
  font-size: 14px;
  padding: 8px 16px;
}

.el-button + .el-button {
  margin-left: 10px;
}

/* 10. 操作按钮样式 */
.table-operate {
  display: flex;
  gap: 5px;
  justify-content: center;
}

/* 11. 标签样式优化 */
.el-tag {
  font-size: 12px;
}

/* 11. 弹窗样式 */
:deep(.el-dialog) {
  border-radius: 8px;
}

:deep(.el-dialog__header) {
  background: #f5f7fa;
  padding: 20px;
  border-radius: 8px 8px 0 0;
}

:deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

/* 12. 描述列表样式 */
:deep(.el-descriptions) {
  margin-top: 10px;
}

:deep(.el-descriptions__label) {
  font-weight: 600;
  color: #606266;
}

:deep(.el-descriptions__content) {
  color: #333;
}

/* 13. 标签页样式 */
:deep(.el-tabs__header) {
  margin-bottom: 20px;
}

:deep(.el-tabs__item) {
  font-size: 16px;
  font-weight: 500;
}

:deep(.el-tabs__item.is-active) {
  color: #409eff;
  font-weight: 600;
}

/* 14. 响应式设计 */
@media (max-width: 768px) {
  .content-container {
    padding: 10px;
  }

  .search-form {
    padding: 12px;
  }

  .search-form .el-form-item {
    margin-right: 10px;
  }

  .rank-form {
    padding: 12px;
  }
}

/* 15. 加载状态优化 */
:deep(.el-loading-mask) {
  border-radius: 8px;
}
</style>