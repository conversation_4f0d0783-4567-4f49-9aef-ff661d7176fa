/**
 * 账号设置模块 - V2版本
 * 按照API封装规范文档实现账号设置相关接口
 */

import { get, post } from '../index'

export default {
  /**
   * 获取管理员列表
   * @param {Object} querys 查询参数
   * @param {number} querys.status 状态，1正常，-1禁用
   * @param {string} querys.username 用户名，非必填
   * @param {number} querys.roleId 角色ID，非必填
   * @param {number} querys.pageNum 当前页数，可选，默认1
   * @param {number} querys.pageSize 每页数量，可选，默认10
   * @returns {Promise} 返回管理员列表数据
   */
  adminList(querys) {
    console.log('👤 管理员列表API-V2请求参数:', querys)
    return get('/api/admin/account/admin/list', querys)
  },

  /**
   * 获取管理员详情
   * @param {Object} querys 查询参数
   * @param {number} querys.id 管理员ID
   * @returns {Promise} 返回管理员详情数据
   */
  adminInfo(querys) {
    if (!querys || !querys.id) {
      return Promise.reject(new Error('管理员ID不能为空'))
    }
    console.log('🔍 获取管理员详情API-V2请求:', querys)
    return get(`/api/admin/account/admin/info/${querys.id}`)
  },

  /**
   * 新增管理员
   * @param {Object} querys 管理员数据
   * @param {string} querys.username 用户名
   * @param {string} querys.password 密码
   * @param {string} querys.realName 真实姓名
   * @param {string} querys.phone 手机号
   * @param {string} querys.email 邮箱
   * @param {number} querys.roleId 角色ID
   * @param {number} querys.status 状态，1正常，-1禁用
   * @returns {Promise} 返回新增结果
   */
  adminAdd(querys) {
    if (!querys || !querys.username || !querys.password) {
      return Promise.reject(new Error('用户名和密码不能为空'))
    }

    const apiData = {
      username: querys.username,
      password: querys.password,
      realName: querys.realName || '',
      phone: querys.phone || '',
      email: querys.email || '',
      roleId: querys.roleId || 1,
      status: querys.status || 1
    }

    console.log('➕ 新增管理员API-V2请求数据:', apiData)
    return post('/api/admin/account/admin/add', apiData)
  },

  /**
   * 编辑管理员
   * @param {Object} querys 编辑数据（包含id）
   * @returns {Promise} 返回编辑结果
   */
  adminUpdate(querys) {
    if (!querys || !querys.id) {
      return Promise.reject(new Error('管理员ID不能为空'))
    }

    console.log('✏️ 编辑管理员API-V2请求:', querys)
    return post('/api/admin/account/admin/update', querys)
  },

  /**
   * 删除管理员
   * @param {Object} querys 删除参数
   * @param {number} querys.id 管理员ID
   * @returns {Promise} 返回删除结果
   */
  adminDelete(querys) {
    if (!querys || !querys.id) {
      return Promise.reject(new Error('管理员ID不能为空'))
    }

    console.log('🗑️ 删除管理员API-V2请求:', querys)
    return post(`/api/admin/account/admin/delete/${querys.id}`)
  },

  /**
   * 更新管理员状态
   * @param {Object} querys 状态更新参数
   * @param {number} querys.id 管理员ID
   * @param {number} querys.status 状态，1正常，-1禁用
   * @returns {Promise} 返回更新结果
   */
  adminStatus(querys) {
    if (!querys || !querys.id) {
      return Promise.reject(new Error('管理员ID不能为空'))
    }

    if (![1, -1].includes(querys.status)) {
      return Promise.reject(new Error('状态值无效'))
    }

    console.log('🔄 更新管理员状态API-V2请求:', querys)
    return post(`/api/admin/account/admin/status/${querys.id}`, { status: querys.status })
  },

  /**
   * 获取角色列表
   * @param {Object} querys 查询参数
   * @param {number} querys.status 状态，1正常，-1禁用
   * @param {string} querys.name 角色名称，非必填
   * @param {number} querys.pageNum 当前页数，可选，默认1
   * @param {number} querys.pageSize 每页数量，可选，默认10
   * @returns {Promise} 返回角色列表数据
   */
  roleList(querys) {
    console.log('🎭 角色列表API-V2请求参数:', querys)
    return get('/api/admin/account/role/list', querys)
  },

  /**
   * 获取角色详情
   * @param {Object} querys 查询参数
   * @param {number} querys.id 角色ID
   * @returns {Promise} 返回角色详情数据
   */
  roleInfo(querys) {
    if (!querys || !querys.id) {
      return Promise.reject(new Error('角色ID不能为空'))
    }
    console.log('🔍 获取角色详情API-V2请求:', querys)
    return get(`/api/admin/account/role/info/${querys.id}`)
  },

  /**
   * 新增角色
   * @param {Object} querys 角色数据
   * @param {string} querys.name 角色名称
   * @param {string} querys.description 角色描述
   * @param {Array} querys.permissions 权限列表
   * @param {number} querys.status 状态，1正常，-1禁用
   * @returns {Promise} 返回新增结果
   */
  roleAdd(querys) {
    if (!querys || !querys.name) {
      return Promise.reject(new Error('角色名称不能为空'))
    }

    const apiData = {
      name: querys.name,
      description: querys.description || '',
      permissions: querys.permissions || [],
      status: querys.status || 1
    }

    console.log('➕ 新增角色API-V2请求数据:', apiData)
    return post('/api/admin/account/role/add', apiData)
  },

  /**
   * 编辑角色
   * @param {Object} querys 编辑数据（包含id）
   * @returns {Promise} 返回编辑结果
   */
  roleUpdate(querys) {
    if (!querys || !querys.id) {
      return Promise.reject(new Error('角色ID不能为空'))
    }

    console.log('✏️ 编辑角色API-V2请求:', querys)
    return post('/api/admin/account/role/update', querys)
  },

  /**
   * 删除角色
   * @param {Object} querys 删除参数
   * @param {number} querys.id 角色ID
   * @returns {Promise} 返回删除结果
   */
  roleDelete(querys) {
    if (!querys || !querys.id) {
      return Promise.reject(new Error('角色ID不能为空'))
    }

    console.log('🗑️ 删除角色API-V2请求:', querys)
    return post(`/api/admin/account/role/delete/${querys.id}`)
  },

  /**
   * 获取菜单列表
   * @param {Object} querys 查询参数
   * @param {number} querys.parentId 父级菜单ID，0为顶级菜单
   * @param {number} querys.status 状态，1正常，-1禁用
   * @returns {Promise} 返回菜单列表数据
   */
  menuList(querys) {
    console.log('📋 菜单列表API-V2请求参数:', querys)
    return get('/api/admin/account/menu/list', querys)
  },

  /**
   * 获取菜单树形结构
   * @returns {Promise} 返回菜单树形数据
   */
  menuTree() {
    console.log('🌳 菜单树形结构API-V2请求')
    return get('/api/admin/account/menu/tree')
  },

  /**
   * 新增菜单
   * @param {Object} querys 菜单数据
   * @param {string} querys.name 菜单名称
   * @param {string} querys.path 菜单路径
   * @param {string} querys.icon 菜单图标
   * @param {number} querys.parentId 父级菜单ID
   * @param {number} querys.sort 排序
   * @param {number} querys.status 状态，1正常，-1禁用
   * @returns {Promise} 返回新增结果
   */
  menuAdd(querys) {
    if (!querys || !querys.name || !querys.path) {
      return Promise.reject(new Error('菜单名称和路径不能为空'))
    }

    const apiData = {
      name: querys.name,
      path: querys.path,
      icon: querys.icon || '',
      parentId: querys.parentId || 0,
      sort: querys.sort || 0,
      status: querys.status || 1
    }

    console.log('➕ 新增菜单API-V2请求数据:', apiData)
    return post('/api/admin/account/menu/add', apiData)
  },

  /**
   * 编辑菜单
   * @param {Object} querys 编辑数据（包含id）
   * @returns {Promise} 返回编辑结果
   */
  menuUpdate(querys) {
    if (!querys || !querys.id) {
      return Promise.reject(new Error('菜单ID不能为空'))
    }

    console.log('✏️ 编辑菜单API-V2请求:', querys)
    return post('/api/admin/account/menu/update', querys)
  },

  /**
   * 删除菜单
   * @param {Object} querys 删除参数
   * @param {number} querys.id 菜单ID
   * @returns {Promise} 返回删除结果
   */
  menuDelete(querys) {
    if (!querys || !querys.id) {
      return Promise.reject(new Error('菜单ID不能为空'))
    }

    console.log('🗑️ 删除菜单API-V2请求:', querys)
    return post(`/api/admin/account/menu/delete/${querys.id}`)
  },

  /**
   * 获取代理商列表
   * @param {Object} querys 查询参数
   * @param {number} querys.status 状态，1正常，-1禁用
   * @param {string} querys.name 代理商名称，非必填
   * @param {string} querys.phone 联系电话，非必填
   * @param {number} querys.pageNum 当前页数，可选，默认1
   * @param {number} querys.pageSize 每页数量，可选，默认10
   * @returns {Promise} 返回代理商列表数据
   */
  franchiseeList(querys) {
    console.log('🏢 代理商列表API-V2请求参数:', querys)
    return get('/api/admin/account/franchisee/list', querys)
  },

  /**
   * 新增代理商
   * @param {Object} querys 代理商数据
   * @param {string} querys.name 代理商名称
   * @param {string} querys.contact 联系人
   * @param {string} querys.phone 联系电话
   * @param {string} querys.address 地址
   * @param {number} querys.commissionRate 佣金比例
   * @param {number} querys.status 状态，1正常，-1禁用
   * @returns {Promise} 返回新增结果
   */
  franchiseeAdd(querys) {
    if (!querys || !querys.name || !querys.contact) {
      return Promise.reject(new Error('代理商名称和联系人不能为空'))
    }

    console.log('➕ 新增代理商API-V2请求数据:', querys)
    return post('/api/admin/account/franchisee/add', querys)
  },

  /**
   * 编辑代理商
   * @param {Object} querys 编辑数据（包含id）
   * @returns {Promise} 返回编辑结果
   */
  franchiseeUpdate(querys) {
    if (!querys || !querys.id) {
      return Promise.reject(new Error('代理商ID不能为空'))
    }

    console.log('✏️ 编辑代理商API-V2请求:', querys)
    return post('/api/admin/account/franchisee/update', querys)
  },

  /**
   * 删除代理商
   * @param {Object} querys 删除参数
   * @param {number} querys.id 代理商ID
   * @returns {Promise} 返回删除结果
   */
  franchiseeDelete(querys) {
    if (!querys || !querys.id) {
      return Promise.reject(new Error('代理商ID不能为空'))
    }

    console.log('🗑️ 删除代理商API-V2请求:', querys)
    return post(`/api/admin/account/franchisee/delete/${querys.id}`)
  },

  // ==================== 代理商管理（新接口） ====================

  /**
   * 获取代理商列表（新接口）
   * @param {Object} querys 查询参数
   * @param {number} querys.pageNum 当前页数，可选，默认1
   * @param {number} querys.pageSize 每页数量，可选，默认10
   * @param {number} querys.status 状态筛选，可选
   * @param {string} querys.username 用户名筛选，可选
   * @returns {Promise} 返回代理商列表数据
   */
  agentList(querys) {
    console.log('🏢 代理商列表API-V2请求参数:', querys)
    return get('/api/admin/manage/agent/list', querys)
  },

  /**
   * 修改代理商状态
   * @param {Object} querys 状态修改参数
   * @param {number} querys.id 代理商ID
   * @returns {Promise} 返回修改结果
   */
  agentStatus(querys) {
    if (!querys || !querys.id) {
      return Promise.reject(new Error('代理商ID不能为空'))
    }
    console.log('🔄 修改代理商状态API-V2请求:', querys)
    return post(`/api/admin/manage/agent/status/${querys.id}`)
  },

  /**
   * 代理商审核
   * @param {Object} querys 审核参数
   * @param {number} querys.id 代理商ID
   * @param {string} querys.examinedText 审核原因
   * @param {number} querys.status 审核状态（0未审核，1审核通过，2审核未通过）
   * @returns {Promise} 返回审核结果
   */
  agentExamine(querys) {
    if (!querys || !querys.id) {
      return Promise.reject(new Error('代理商ID不能为空'))
    }
    console.log('✅ 代理商审核API-V2请求:', querys)
    return post('/api/admin/manage/agent/examine', querys)
  },

  /**
   * 新增代理商
   * @param {Object} querys 代理商数据
   * @param {number} querys.userId 用户ID，可选
   * @param {string} querys.username 账号
   * @param {string} querys.password 密码，传md5，不传后台设置默认值
   * @param {number} querys.type 代理类型：1省，2市，3区县
   * @param {Array} querys.cityId 代理城市ID列表
   * @param {string} querys.legalPersonName 法人姓名
   * @param {string} querys.legalPersonIdCard 法人身份证号
   * @param {string} querys.legalPersonTel 法人手机号
   * @param {string} querys.legalPersonIdCardImg1 身份证正面照URL
   * @param {string} querys.legalPersonIdCardImg2 身份证反面照URL
   * @param {string} querys.legalPersonLicense 营业执照图片URL
   * @returns {Promise} 返回新增结果
   */
  agentAdd(querys) {
    if (!querys || !querys.username) {
      return Promise.reject(new Error('账号不能为空'))
    }
    console.log('➕ 新增代理商API-V2请求数据:', querys)
    return post('/api/admin/manage/agent/add', querys)
  },

  /**
   * 修改代理商
   * @param {Object} querys 代理商数据（包含id）
   * @param {number} querys.id 代理商ID，必填
   * @returns {Promise} 返回修改结果
   */
  agentEdit(querys) {
    if (!querys || !querys.id) {
      return Promise.reject(new Error('代理商ID不能为空'))
    }
    console.log('✏️ 修改代理商API-V2请求:', querys)
    return post('/api/admin/manage/agent/edit', querys)
  },

  /**
   * 修改代理商默认密码
   * @returns {Promise} 返回修改结果
   */
  agentDefaultPassword() {
    console.log('🔑 修改代理商默认密码API-V2请求')
    return post('/api/admin/manage/default/102')
  },

  /**
   * 获取代理城市树形结构
   * @returns {Promise} 返回城市树形数据
   */
  agentCityTree() {
    console.log('🌳 获取代理城市树API-V2请求')
    return get('/api/admin/manage/tree')
  },

  /**
   * 更新管理员密码
   * @param {Object} data 密码数据
   * @param {string} data.newPassword 新密码（MD5加密）
   * @param {string} data.confirmPassword 确认密码（MD5加密）
   * @returns {Promise} 返回更新结果
   */
  updatePassword(data) {
    console.log('🔐 更新管理员密码API-V2请求')
    return post('/api/admin/login/updatePass', data)
  },

  /**
   * 获取第三方配置列表
   * @param {Object} querys 查询参数
   * @param {string} querys.type 第三方类型，wechat/alipay/sms等
   * @returns {Promise} 返回第三方配置列表
   */
  thirdPartyList(querys) {
    console.log('🔗 第三方配置列表API-V2请求参数:', querys)
    return get('/api/admin/account/third/list', querys)
  },

  /**
   * 获取第三方配置详情
   * @param {Object} querys 查询参数
   * @param {number} querys.id 配置ID
   * @returns {Promise} 返回配置详情
   */
  thirdPartyInfo(querys) {
    if (!querys || !querys.id) {
      return Promise.reject(new Error('配置ID不能为空'))
    }
    console.log('🔍 获取第三方配置详情API-V2请求:', querys)
    return get(`/api/admin/account/third/info/${querys.id}`)
  },

  /**
   * 更新第三方配置
   * @param {Object} querys 配置数据
   * @param {number} querys.id 配置ID
   * @param {string} querys.type 第三方类型
   * @param {Object} querys.config 配置参数
   * @param {number} querys.status 状态，1启用，0禁用
   * @returns {Promise} 返回更新结果
   */
  thirdPartyUpdate(querys) {
    if (!querys || !querys.id) {
      return Promise.reject(new Error('配置ID不能为空'))
    }

    console.log('✏️ 更新第三方配置API-V2请求:', querys)
    return post('/api/admin/account/third/update', querys)
  }
}
